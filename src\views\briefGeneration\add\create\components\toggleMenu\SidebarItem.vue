<template>
  <div v-if="!item.hidden">
    <template v-if="!item.children || item.children.length === 0">
      <el-menu-item
        :index="String(item.id)"
        @click="() => handleMenuClick(item)"
        style="width: 100%"
        :disabled="startGenerating"
      >
        <template #title>
          <span class="menu-title" :title="item.name">{{ item.name }}</span>
        </template>
      </el-menu-item>
    </template>
    <el-sub-menu v-else ref="subMenu" :index="String(item.id)" teleported>
      <template #title>
        <div class="menu-title">
          <span :title="item.name">{{ item.name }}</span>
        </div>
      </template>
      <sidebar-item
        v-for="(child, i) in item.children"
        :key="i"
        :is-nest="true"
        :item="child"
        class="nest-menu"
      />
    </el-sub-menu>
  </div>
</template>

<script setup>
import { isExternal } from "@/utils/validate";
import AppLink from "./Link";
import { getNormalPath } from "@/utils/ruoyi";
import { startGenerating } from "@/views/briefGeneration/add/create/js/sharedState.js";
const props = defineProps({
  // route object
  item: {
    type: Object,
    required: true,
  },
});
function handleMenuClick(menuItem) {
  const id = String(menuItem.id).replace(/^\//, ""); // 去掉前导斜杠
  const el = document.getElementById(id);
  console.log('id,el :>> ', id,el);
  if (el) {
    el.scrollIntoView({ behavior: "smooth", block: "start" });
  }
}
</script>
<style lang="scss" scoped>
/*隐藏文字*/
.el-menu--collapse .el-sub-menu__title span {
  display: none;
}
/*隐藏 > */
.el-menu--collapse .el-sub-menu__title .el-submenu__icon-arrow {
  display: none;
}
.el-menu--collapse .el-sub-menu.is-active {
  background: #1e4462;
}
.el-menu-item.is-active {
  background: #1e4462;
}
.el-menu-item.is-active,
.el-sub-menu.is-active {
  position: relative; // 让伪元素定位参照
}
.el-menu-item.is-active,
.el-sub-menu.is-active > .el-sub-menu__title {
  position: relative;
}

.el-menu-item.is-active::after,
.el-sub-menu.is-active > .el-sub-menu__title::after,
.el-menu-item:hover::after,
.el-sub-menu:hover > .el-sub-menu__title::after {
  content: "";
  display: block;
  position: absolute;
  right: 0;
  top: 0;
  width: 2.5px;
  height: 100%;
  background: #6AF6FF;
  border-radius: 2px 0 0 2px;
  z-index: 1;
}
.menu-title {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
