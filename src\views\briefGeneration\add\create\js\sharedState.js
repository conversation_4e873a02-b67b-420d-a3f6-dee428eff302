import { ref } from "vue";

export const paragraphs = ref([
  // {
  //   title: "一、知识点描述", //一级标题
  //   mode: "view",
  //   content: "",
  //   subSections: [
  //     {
  //       title: "1.知识点描述2", //二级标题
  //       content: "紧耦合问题：继承会导致子类与父类之间高度耦合，父类的修改可能会影响所有子类，增加了代码的脆弱性。多重继承的复杂性：多重继承容易引发“菱形问题”（Diamond",
  //       mode: "view",
  //     },
  //   ],
  // },
]);
// export const saveParagraphs = ref([]); // 用于保存编辑器内容的副本,判断是否有修改
export const startGenerating = ref(false);

export const operatingTextIndex = ref(); // 当前操作的文本段落索引

// 右侧对话窗口
export const popType = ref(false);

// 当前编辑器实例
export const currentEditor = ref(null);
// 左侧目录菜单
export const menuList = ref([]);
export const activeMenu = ref(null); // 当前选中的菜单 value
export const scrollRef = ref(null); // 当前滚动的元素
// 一键生成简报配置页面
export const form = ref({
  title: "",
  subject: "",
  stage: "", // 研究阶段的默认值
  businessVersion: "1", // 行业分析框架的默认值
  competeVersion: "1", // 竞争分析框架的默认值
  clientVersion: "1", // 顾客认知分析框架的默认值
  manageVersion: "1", // 企业经营分析框架的默认值
  entrepreneurVersion: "1", // 企业家分析框架的默认值
  knowledge: false,
  internet: false,
});
// 当前流程
export const activeStep = ref(1);

export const pageType = ref("add"); // 页面类型，add: 新建，edit: 编辑,view: 详情
export const pageId = ref(null); // 页面简报ID，编辑和详情时使用
export const referId = ref(0); // 版本id  0是全新简报，没有历史版本
export const resetForm = (type) => {
  if (type != "return") {
    // localStorage.removeItem("myForm");
    localStorage.removeItem("configData");
    // form.value = {};
    configData.value = {
      title: "",
      brandName: "",
      version: 1, // 简报版本
      dataBank: false,
      internet: false,
      labelConfigId: null,
    };
  }
  popType.value = false;
  referId.value = 0;
  paragraphs.value = [];
  startGenerating.value = false;
  operatingTextIndex.value = null;
  menuList.value = [];
  currentEditor.value = null;
  activeMenu.value = null;
  scrollRef.value = null;
  ishumanInput.value = false;
  competitorParams.value = {
    node_id: null,
    conversation_id: null,
    variable_key: "text",
  };
  top5Options.value = [];
};

// 一键生成简报配置页面
export const configData = ref({
  title: "",
  brandName: "",
  version: 1, // 简报版本
  dataBank: false,
  internet: false,
  labelConfigId: null,
});
// 竞品所需参数
export const ishumanInput = ref(false);
export const competitorParams = ref({
  node_id: null,
  conversation_id: null,
  variable_key: "text",
});
export const top5Options = ref([]);
