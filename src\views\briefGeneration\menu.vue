<template>
  <div class="menu-container">
    <div class="logo">
      <img src="/images/logo-kmind.png">
    </div>
    <div class="menu-list">
      <template v-for="(item, index) in menuItems" :key="index">
        <div :class="['menu-item', {active: index === activeIndex}]" :index="item.path" @click="goto(item)">
          <inline-svg :src="item.icon" :title="item.title" />
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import InlineSvg from 'vue-inline-svg'

const props = defineProps({
  showRightBorder: {
    type: Boolean,
    default: true
  }
})

const router = useRouter()
const route = useRoute()

const menuItems = [
  {
    title: "首页",
    path: "/briefGeneration/home",
    icon: "/icons/home.svg",
    childPaths: []
  },
  {
    title: "简报生成",
    path: "/briefGeneration/list",
    icon: "/icons/briefing.svg",
    childPaths: []
  },
  {
    title: "项目管理",
    path: "",
    icon: "/icons/book.svg",
    childPaths: []
  },
  {
    title: "知识管理",
    path: "",
    icon: "/icons/book2.svg",
    childPaths: []
  },
  {
    title: "客户管理",
    path: "",
    icon: "/icons/customer.svg",
    childPaths: []
  },
  {
    title: "系统管理",
    path: "",
    icon: "/icons/apps.svg",
    childPaths: []
  },
]

const activeIndex = computed(() => {
  const curPath = route.path

  for (let idx=0; idx < menuItems.length; idx++) {
    const item = menuItems[idx]
    if (item.path === curPath || item.childPaths.includes(curPath)) {
      return idx
    }
  }

  return -1
})

const goto = (item) => {
  if (item.path) {
    router.push(item.path)
  }
}
</script>

<style lang="scss" scoped>
.menu-container {
  width: 100%;
  padding: 24px 22px 24px 12px;
  border-radius: 0px 20px 20px 0px;
  border-right: 1px solid #6AF6FF;

  .logo {
    img {
      width: 114px;
      height: 29px;
    }
  }

  .menu-list {
    margin-top: 68px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;

    .menu-item {
      width: 52px;
      height: 58px;
      background-color: rgba(63, 120, 141, 0.8);
      border-radius: 10px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      margin-bottom: 21px;
      border: 0;
      cursor: pointer;

      svg {
        width: 18px;
        height: 18px;
        color: #63E5FE;
        transition: transform 500ms ease;
      }

      &:hover, &.active {
        background: linear-gradient(-6deg, #3A6A7D, #60B7C8);
        box-shadow: 2px 2px 1px 0px #00E5FF;

        svg {
          width: 23px;
          height: 23px;
          color: white;
          transform: scale(1.2, 1.2);
        }
      }
    }
  }
}
</style>
