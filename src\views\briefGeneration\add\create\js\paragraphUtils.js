import { paragraphs, menuList, startGenerating } from "./sharedState.js";

/**
 * 将所有项设置为 view 模式
 * 遍历多维数组结构的paragraphs，将所有段落及其子段落的mode设置为"view"
 */
export const setAllItemsToViewMode = () => {
  paragraphs.value.forEach((paragraphsItem) => {
    paragraphsItem?.map((v) => {
      v.mode = "view";
      if (v.subSections) {
        v.subSections = v.subSections.map((sub) => {
          sub.mode = "view";
          if (sub.subSections) {
            sub.subSections = sub.subSections.map((thirdSub) => {
              thirdSub.mode = "view";
              return thirdSub;
            });
          }
          return sub;
        });
      }
    });
  });
};

/**
 * 将当前操作的项设置为 edit 模式
 * 根据索引字符串定位到具体的段落并设置为编辑模式
 * @param {string} indexStr - 索引字符串，格式如 "menuIndex-paragraphIndex-subIndex-thirdIndex"
 */
export const setCurrentItemToEditMode = (indexStr) => {
  const indexs = String(indexStr)?.split("-");

  if (indexs.length === 2) {
    // 二级编辑 (menuIndex-paragraphIndex) - 正式段落
    const [menuIndex, paragraphIndex] = indexs.map(Number);
    const targetParagraph = paragraphs.value[menuIndex]?.[paragraphIndex];
    if (targetParagraph) {
      targetParagraph.mode = "edit";
    }
  } else if (indexs.length === 3) {
    // 三级编辑 (menuIndex-paragraphIndex-subIndex)
    const [menuIndex, paragraphIndex, subIndex] = indexs.map(Number);
    const targetParagraph = paragraphs.value[menuIndex]?.[paragraphIndex];
    const targetSub = targetParagraph?.subSections?.[subIndex];
    if (targetSub) {
      targetSub.mode = "edit";
    }
  } else if (indexs.length === 4) {
    // 四级编辑 (menuIndex-paragraphIndex-subIndex-thirdIndex)
    const [menuIndex, paragraphIndex, subIndex, thirdIndex] =
      indexs.map(Number);
    const targetParagraph = paragraphs.value[menuIndex]?.[paragraphIndex];
    const targetSub = targetParagraph?.subSections?.[subIndex];
    const targetThird = targetSub?.subSections?.[thirdIndex];
    if (targetThird) {
      targetThird.mode = "edit";
    }
  }
};
// 刷新目录;
export function refreshSideBar() {
  if (startGenerating.value) {
    ElMessage.error("正在生成中，请稍后再试");
    return;
  }
  // 遍历每个菜单项，更新其children
  menuList.value.forEach((menuItem, menuIndex) => {
    // 检查对应的paragraphs数据是否存在
    if (paragraphs.value[menuIndex]) {
      // 从paragraphs数据中提取type为2的段落作为children
      menuItem.children = paragraphs.value[menuIndex]
        .map((p, idx) => ({
          id: `/section${menuIndex}-${idx}`,
          name: p.title || `段落${idx + 1}`,
          type: p.type,
          children: (p.subSections || [])
            .map((sub, subIdx) => ({
              id: `/section${menuIndex}-${idx}-${subIdx}`,
              name: sub.title || `子段落${subIdx + 1}`,
              type: sub.type,
            }))
            .filter((sub) => sub.type == 3),
        }))
        .filter((p) => p.type == 2);
    } else {
      // 如果没有对应的paragraphs数据，清空children
      menuItem.children = [];
    }
  });
}
