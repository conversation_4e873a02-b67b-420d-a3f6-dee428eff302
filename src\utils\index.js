import { parseTime } from './ruoyi'

/**
 * 表格时间格式化
 */
export function formatDate(cellValue) {
  if (cellValue == null || cellValue == "") return "";
  var date = new Date(cellValue) 
  var year = date.getFullYear()
  var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
  var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate() 
  var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours() 
  var minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes() 
  var seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url
  const search = url.substring(url.lastIndexOf('?') + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}

/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength(str) {
  // returns the byte length of an utf8 string
  let s = str.length
  for (var i = str.length - 1; i >= 0; i--) {
    const code = str.charCodeAt(i)
    if (code > 0x7f && code <= 0x7ff) s++
    else if (code > 0x7ff && code <= 0xffff) s += 2
    if (code >= 0xDC00 && code <= 0xDFFF) i--
  }
  return s
}

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
  const newArray = []
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i])
    }
  }
  return newArray
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
  if (!json) return ''
  return cleanArray(
    Object.keys(json).map(key => {
      if (json[key] === undefined) return ''
      return encodeURIComponent(key) + '=' + encodeURIComponent(json[key])
    })
  ).join('&')
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach(v => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

/**
 * 简单的HTML转文本函数
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val) {
  const div = document.createElement('div')
  div.innerHTML = val
  return div.textContent || div.innerText
}

/**
 * 高级HTML转文本函数，根据标签类型选择性保持HTML格式
 * @param {string} htmlString - HTML字符串
 * @param {Object} options - 配置选项
 * @returns {string} 格式化的纯文本
 */
export function htmlToFormattedText(htmlString, options = {}) {
  if (!htmlString) return '';

  const defaultOptions = {
    preserveLineBreaks: true,        // 保持换行
    preserveSpacing: true,           // 保持空格
    removeExtraSpaces: true,         // 移除多余空格
    keepHtmlTags: ['a', 'img', 'table', 'thead', 'tbody', 'tr', 'td', 'th'], // 保持HTML格式的标签
    textOnlyTags: ['div', 'p', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'], // 只提取文本的标签
    addLineBreakTags: ['div', 'p', 'br', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'] // 需要添加换行的标签
  };

  const config = { ...defaultOptions, ...options };

  // 创建临时DOM元素
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlString;

  // 处理元素，选择性保持HTML格式
  const result = processElementsSelectively(tempDiv, config);

  // 后处理
  const text = postProcessText(result, config);

  return text.trim();
}

/**
 * 选择性处理HTML元素，根据标签类型决定是保持HTML格式还是提取文本
 */
function processElementsSelectively(element, config) {
  const result = [];

  // 递归处理所有子节点
  function processNode(node) {
    if (node.nodeType === Node.TEXT_NODE) {
      // 文本节点直接返回文本内容
      const text = node.textContent || '';
      if (text.trim()) {
        result.push(text);
      }
      return;
    }

    if (node.nodeType === Node.ELEMENT_NODE) {
      const tagName = node.tagName.toLowerCase();

      // 需要保持HTML格式的标签
      if (config.keepHtmlTags.includes(tagName)) {
        result.push(node.outerHTML);
        return;
      }

      // 只提取文本的标签
      if (config.textOnlyTags.includes(tagName)) {
        const textContent = node.textContent || node.innerText || '';
        if (textContent.trim()) {
          result.push(textContent);

          // 如果是需要添加换行的标签，添加换行符
          if (config.addLineBreakTags.includes(tagName)) {
            result.push('\n');
          }
        }
        return;
      }

      // br 标签特殊处理
      if (tagName === 'br') {
        result.push('\n');
        return;
      }

      // 其他标签递归处理子节点
      for (let child of node.childNodes) {
        processNode(child);
      }
    }
  }

  // 处理所有子节点
  for (let child of element.childNodes) {
    processNode(child);
  }

  return result.join('');
}

/**
 * 处理特殊HTML元素（保留原函数以兼容性）
 */
function processSpecialElements(element, config) {
  // 处理换行标签
  const brElements = element.querySelectorAll('br');
  brElements.forEach(br => {
    br.replaceWith(document.createTextNode('\n'));
  });

  // 处理段落
  const pElements = element.querySelectorAll('p');
  pElements.forEach(p => {
    p.appendChild(document.createTextNode('\n\n'));
  });

  // 处理标题
  const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
  headings.forEach(heading => {
    const level = parseInt(heading.tagName.charAt(1));
    const prefix = '#'.repeat(level) + ' ';
    heading.insertBefore(document.createTextNode(prefix), heading.firstChild);
    heading.appendChild(document.createTextNode('\n\n'));
  });

  // 处理列表
  if (config.addListMarkers) {
    processLists(element);
  }

  // 处理链接
  if (config.preserveLinks) {
    processLinks(element, config);
  }

  // 处理表格
  if (config.addTableBorders) {
    processTables(element);
  }

  // 处理代码块
  const codeBlocks = element.querySelectorAll('pre, code');
  codeBlocks.forEach(code => {
    if (code.tagName === 'PRE') {
      code.insertBefore(document.createTextNode('\n```\n'), code.firstChild);
      code.appendChild(document.createTextNode('\n```\n'));
    } else {
      code.insertBefore(document.createTextNode('`'), code.firstChild);
      code.appendChild(document.createTextNode('`'));
    }
  });

  // 处理强调
  const strongElements = element.querySelectorAll('strong, b');
  strongElements.forEach(strong => {
    strong.insertBefore(document.createTextNode('**'), strong.firstChild);
    strong.appendChild(document.createTextNode('**'));
  });

  const emElements = element.querySelectorAll('em, i');
  emElements.forEach(em => {
    em.insertBefore(document.createTextNode('*'), em.firstChild);
    em.appendChild(document.createTextNode('*'));
  });

  // 处理分隔线
  const hrElements = element.querySelectorAll('hr');
  hrElements.forEach(hr => {
    hr.replaceWith(document.createTextNode('\n---\n'));
  });
}

/**
 * 处理列表元素
 */
function processLists(element) {
  const lists = element.querySelectorAll('ul, ol');
  lists.forEach(list => {
    const isOrdered = list.tagName === 'OL';
    const items = list.querySelectorAll('li');

    items.forEach((item, index) => {
      const marker = isOrdered ? `${index + 1}. ` : '• ';
      item.insertBefore(document.createTextNode(marker), item.firstChild);
      item.appendChild(document.createTextNode('\n'));
    });

    list.appendChild(document.createTextNode('\n'));
  });
}

/**
 * 处理链接元素
 */
function processLinks(element, config) {
  const links = element.querySelectorAll('a[href]');
  links.forEach(link => {
    const text = link.textContent || link.innerText || '';
    const href = link.getAttribute('href') || '';

    if (text && href && text !== href) {
      const linkText = config.linkFormat
        .replace('text', text)
        .replace('url', href);
      link.replaceWith(document.createTextNode(linkText));
    }
  });
}

/**
 * 处理表格元素
 */
function processTables(element) {
  const tables = element.querySelectorAll('table');
  tables.forEach(table => {
    const rows = table.querySelectorAll('tr');
    rows.forEach((row, rowIndex) => {
      const cells = row.querySelectorAll('td, th');
      cells.forEach((cell, cellIndex) => {
        if (cellIndex > 0) {
          cell.insertBefore(document.createTextNode(' | '), cell.firstChild);
        }
      });
      row.appendChild(document.createTextNode('\n'));

      // 为表头添加分隔线
      if (rowIndex === 0 && row.querySelector('th')) {
        const separator = '|' + ' --- |'.repeat(cells.length) + '\n';
        row.appendChild(document.createTextNode(separator));
      }
    });
    table.appendChild(document.createTextNode('\n'));
  });
}

/**
 * 后处理文本
 */
function postProcessText(text, config) {
  if (config.removeExtraSpaces) {
    // 移除多余的空格，但保留单个空格
    // 注意：不要影响HTML标签内的空格
    text = text.replace(/(?<!<[^>]*)\s+(?![^<]*>)/g, ' ');
  }

  if (config.preserveLineBreaks) {
    // 规范化换行符
    text = text.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
    // 移除过多的连续换行（超过2个）
    text = text.replace(/\n{3,}/g, '\n\n');
    // 清理行首行尾的空格
    text = text.replace(/^\s+|\s+$/gm, '');
  } else {
    // 将换行符替换为空格
    text = text.replace(/\n+/g, ' ');
  }

  return text;
}

/**
 * 文本换行处理
 */
function wrapText(text, maxLength) {
  const lines = text.split('\n');
  const wrappedLines = [];

  lines.forEach(line => {
    if (line.length <= maxLength) {
      wrappedLines.push(line);
    } else {
      const words = line.split(' ');
      let currentLine = '';

      words.forEach(word => {
        if ((currentLine + word).length <= maxLength) {
          currentLine += (currentLine ? ' ' : '') + word;
        } else {
          if (currentLine) {
            wrappedLines.push(currentLine);
          }
          currentLine = word;
        }
      });

      if (currentLine) {
        wrappedLines.push(currentLine);
      }
    }
  });

  return wrappedLines.join('\n');
}

/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
  if (typeof target !== 'object') {
    target = {}
  }
  if (Array.isArray(source)) {
    return source.slice()
  }
  Object.keys(source).forEach(property => {
    const sourceProperty = source[property]
    if (typeof sourceProperty === 'object') {
      target[property] = objectMerge(target[property], sourceProperty)
    } else {
      target[property] = sourceProperty
    }
  })
  return target
}

/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass(element, className) {
  if (!element || !className) {
    return
  }
  let classString = element.className
  const nameIndex = classString.indexOf(className)
  if (nameIndex === -1) {
    classString += '' + className
  } else {
    classString =
      classString.substr(0, nameIndex) +
      classString.substr(nameIndex + className.length)
  }
  element.className = classString
}

/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type) {
  if (type === 'start') {
    return new Date().getTime() - 3600 * 1000 * 24 * 90
  } else {
    return new Date(new Date().toDateString())
  }
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result

  const later = function() {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function(...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'deepClone')
  }
  const targetObj = source.constructor === Array ? [] : {}
  Object.keys(source).forEach(keys => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys])
    } else {
      targetObj[keys] = source[keys]
    }
  })
  return targetObj
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
  return Array.from(new Set(arr))
}

/**
 * @returns {string}
 */
export function createUniqueString() {
  const timestamp = +new Date() + ''
  const randomNum = parseInt((1 + Math.random()) * 65536) + ''
  return (+(randomNum + timestamp)).toString(32)
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'))
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += ' ' + cls
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)')
    ele.className = ele.className.replace(reg, ' ')
  }
}

export function makeMap(str, expectsLowerCase) {
  const map = Object.create(null)
  const list = str.split(',')
  for (let i = 0; i < list.length; i++) {
    map[list[i]] = true
  }
  return expectsLowerCase
    ? val => map[val.toLowerCase()]
    : val => map[val]
}
 
export const exportDefault = 'export default '

export const beautifierConf = {
  html: {
    indent_size: '2',
    indent_char: ' ',
    max_preserve_newlines: '-1',
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: 'separate',
    brace_style: 'end-expand',
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: false,
    end_with_newline: true,
    wrap_line_length: '110',
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true
  },
  js: {
    indent_size: '2',
    indent_char: ' ',
    max_preserve_newlines: '-1',
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: 'normal',
    brace_style: 'end-expand',
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: true,
    end_with_newline: true,
    wrap_line_length: '110',
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true
  }
}

// 首字母大小
export function titleCase(str) {
  return str.replace(/( |^)[a-z]/g, L => L.toUpperCase())
}

// 下划转驼峰
export function camelCase(str) {
  return str.replace(/_[a-z]/g, str1 => str1.substr(-1).toUpperCase())
}

export function isNumberStr(str) {
  return /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(str)
}

window.uniqIdCounter = 0
export function uniqId() {
  window.uniqIdCounter += 1
  const now = Date.now()
  return `uniqId-${now}-${window.uniqIdCounter}`
}
 
