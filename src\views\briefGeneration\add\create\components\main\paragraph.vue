<template>
  <div
    class="paragraph-item"
    v-if="
      (think && think.trim().length > 0) ||
      (title && title.trim().length > 0) ||
      (subSections && subSections.length > 0) ||
      (mode === 'view' && content && content.trim().length > 0) ||
      (mode === 'edit' && content !== null && content !== undefined)
    "
  >
    <slot name="before"></slot>
    <template v-if="think && think.trim().length > 0">
      <div class="think-con">
        <el-collapse
          class="collapseBox"
          :model-value="collapseStatus[nodeId] || []"
          @change="(val) => (collapseStatus[nodeId] = val)"
        >
          <el-collapse-item name="collapse">
            <template #title>
              <div class="think-title">思考过程：</div>
            </template>
            <md
              :cnt="think"
              :enableHtml="true"
              class="markdown-cnt think-content"
            ></md>
          </el-collapse-item>
        </el-collapse>
      </div>
    </template>
    <div
      :class="[
        'paragraph-container',
        { 'is-busy': startGenerating },
        mode,
        { 'is-child': subSections && subSections.length > 0 },
      ]"
      :id="nodeId"
    >
      <div class="header">
        <!-- 标题编辑功能 -->
        <div class="title-container">
          <span
            v-show="!titleEditMode"
            class="title"
            @click="handleTitleEdit"
            :class="{ editable: !readonly }"
          >
            {{ title }}
          </span>
          <el-input
            v-show="titleEditMode"
            v-model="editableTitle"
            ref="titleInputRef"
            @blur="handleTitleSave"
            @keyup.enter="handleTitleSave"
            class="title-input"
            :maxlength="50"
          />
        </div>
        <!-- 操作按钮组 -->
        <div class="action-buttons" v-if="!readonly && !startGenerating">
          <!-- 添加同级内容按钮 -->
          <el-tooltip content="添加同级内容" placement="top">
            <div
              class="action-btn"
              @click.stop="handleAddSibling"
              v-show="type == 2 || type == 3|| type == 4"
            >
              <i class="ri-add-line"></i>
            </div>
          </el-tooltip>

          <!-- 添加子级内容按钮 -->
          <el-tooltip content="添加子级内容" placement="top">
            <div
              class="action-btn"
              @click.stop="handleAddChild"
              v-show="type == 2 || type == 3"
            >
              <i class="ri-node-tree"></i>
            </div>
          </el-tooltip>

          <!-- 删除按钮 -->
          <el-tooltip content="删除内容" placement="top">
            <div class="action-btn delete-btn" @click.stop="handleDelete">
              <i class="ri-delete-bin-line"></i>
            </div>
          </el-tooltip>
        </div>
      </div>
      <div
        class="nochild"
        v-if="
          (mode === 'view' && content && content.trim().length > 0) ||
          (mode === 'edit' && content !== null && content !== undefined)
        "
        ref="container"
      >
        <div
          v-if="!startGenerating && !readonly"
          class="iconChat2"
          @click.stop="handleEdit"
          alt="star"
        >
          <i class="ri-edit-2-line" style="font-size: 15px"></i>
        </div>
        <md
          v-if="mode === 'view'"
          :cnt="content || '暂无内容'"
          :enableHtml="true"
          class="markdown-cnt"
        ></md>
        <div v-else>
          <element-plus-tiptap
            :model-value="content || ''"
            :enableHtml="true"
            :items="[
              'undo',
              'redo',
              '|',
              'heading1',
              'heading2',
              'heading3',
              '|',
              'bold',
              'italic',
              'strikethrough',
              'underline',
              '|',
              'bulletedList',
              'numberedList',
              '|',
              'insertTable',
              'deleteTable',
              '|',
              'addRowBefore',
              'addRowAfter',
              'deleteRow',
              '|',
              'addColBefore',
              'addColAfter',
              'deleteColumn',
              '|',
              'mergeOrSplitCell',
              '|',
              'link',
              'unlink',
            ]"
            :auto-height="true"
            type="markdown"
            locale="zh_CN"
            @update:model-value="(val) => $emit('update:content', val)"
            class="briefStyle"
            ref="tiptapRef"
          />
        </div>
      </div>
      <div class="ischild" v-if="subSections && subSections.length > 0">
        <paragraph
          v-for="(subSection, subIndex) in subSections"
          :key="subIndex"
          :nodeId="`section${index}-${subIndex}`"
          :title="subSection.title"
          :index="index + '-' + subIndex"
          :content="subSection.content"
          :think="subSection.think"
          :type="subSection.type"
          :mode="subSection.mode"
          v-model:content="subSection.content"
          v-model:subSections="subSection.subSections"
          :readonly="readonly"
          :collapse-status="collapseStatus"
          @update:title="(idx, newTitle) => handleSubTitleUpdate(idx, newTitle)"
          @add-sibling="(idx, type) => $emit('add-sibling', idx, type)"
          @add-child="(idx, type) => $emit('add-child', idx, type)"
          @delete-section="(idx) => $emit('delete-section', idx)"
        >
        </paragraph>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  inject,
  ref,
  onUnmounted,
  defineEmits,
  nextTick,
  watch,
  shallowRef,
  onMounted,
} from "vue";
import { ElMessageBox } from "element-plus";
import {
  paragraphs,
  startGenerating,
  operatingTextIndex,
  popType,
  currentEditor,
  activeStep,
  menuList,
} from "@/views/briefGeneration/add/create/js/sharedState.js";
import {
  setAllItemsToViewMode,
  setCurrentItemToEditMode,
} from "@/views/briefGeneration/add/create/js/paragraphUtils.js";
import { ElMessage } from "element-plus";
import ElementPlusTiptap from "@/components/ElementPlusTiptap/index.vue";
import Md from "@/components/Markdown/index.vue";
import "@/components/ElementPlusTiptap/theme.css";
const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  content: {
    required: true,
  },
  index: {
    required: true,
  },
  nodeId: {
    type: String,
    required: true,
  },
  mode: {
    type: String,
    required: false,
    default: "view",
  },
  type: {
    type: [String, Number],
    required: false,
    default: 1,
  },
  readonly: {
    type: Boolean,
    required: false,
    default: false,
  },
  subSections: {
    type: Array,
    required: false,
    default: () => [],
  },
  think: {
    type: String,
    required: false,
    default: "",
  },
  collapseStatus: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits([
  "update:content",
  "edit",
  "update:title",
  "add-sibling",
  "add-child",
  "delete-section",
]);
const showRightPanel = inject("showRightPanel");
const container = shallowRef(null);
const tiptapRef = ref(null);
const titleInputRef = ref(null);

// 标题编辑相关状态
const titleEditMode = ref(false);
const editableTitle = ref(props.title);
watch(
  () => tiptapRef.value,
  () => {
    currentEditor.value = tiptapRef.value?.context?.editor.value;
  },
  { immediate: true }
);
watch(
  () => operatingTextIndex.value,
  (newVal) => {
    if (newVal && newVal != -1) {
      // 首先将所有项设置为 view 模式
      setAllItemsToViewMode();

      // 然后将当前操作的项设置为 edit 模式
      setCurrentItemToEditMode(newVal);
      showRightPanel(true);
      activeStep.value = 3;
      setTimeout(() => {
        // 检查是否有其他元素正在获取焦点
        if (document.activeElement?.tagName === 'INPUT') {
          // 如果有输入框正在获取焦点，延迟更长时间
          setTimeout(() => {
            tiptapRef.value?.context?.editor?.value
              ?.chain()
              ?.focus()
              ?.selectNodeForward()
              ?.run();
          }, 200);
        } else {
          tiptapRef.value?.context?.editor?.value
            ?.chain()
            ?.focus()
            ?.selectNodeForward()
            ?.run();
        }
      }, 100);
    }
  },
  { immediate: true }
);

// 编辑
const handleEdit = () => {
  if (props.readonly) {
    return;
  }
  if (startGenerating.value) {
    ElMessage.warning("正在进行AI操作, 请等待完成后再进行操作");
    return;
  }
  operatingTextIndex.value = props.index;
};

// 标题编辑相关方法
const handleTitleEdit = () => {
  if (props.readonly || startGenerating.value) {
    return;
  }
  titleEditMode.value = true;
  editableTitle.value = props.title;
  nextTick(() => {
    // 延迟获取焦点，避免与其他元素冲突
    setTimeout(() => {
      if (titleInputRef.value && titleInputRef.value.focus) {
        titleInputRef.value.focus();
      }
    }, 100);
  });
};

const handleTitleSave = () => {
  titleEditMode.value = false;
  if (editableTitle.value.trim() !== props.title) {
    emit("update:title", props.index, editableTitle.value.trim());
  }
};

// 处理子标题更新
const handleSubTitleUpdate = (_subIndex, newTitle) => {
  emit("update:title", _subIndex, newTitle);
};

// 添加同级内容
const handleAddSibling = () => {
  emit("add-sibling", props.index, props.type);
};

// 添加子级内容
const handleAddChild = () => {
  emit("add-child", props.index, Number(props.type) + 1);
};

// 删除功能
const handleDelete = () => {
  const hasSubSections = props.subSections && props.subSections.length > 0;
  const confirmMessage = hasSubSections
    ? `确定删除"${props.title}"吗？删除后将同时删除所有子级内容，此操作不可撤销。`
    : `确定删除"${props.title}"吗？此操作不可撤销。`;

  ElMessageBox.confirm(confirmMessage, "删除确认", {
    confirmButtonText: "确定删除",
    cancelButtonText: "取消",
    customClass: "junzhiMessage",
    type: "warning",
    dangerouslyUseHTMLString: true,
  })
    .then(() => {
      emit("delete-section", props.index);
      ElMessage.success("删除成功");
    })
    .catch(() => {
      // 用户取消删除
    });
};

// --------- 鼠标在外点击，且不是在右侧面板上时，结束编辑 ------------------------

let clickListener = null;
const handleOutsideClick = (event) => {
  const el = container.value;
  // console.log("event.target :>> ", event.target);
  // 点击在弹层上
  if (!hasAncestorWithId(event.target, "app")) {
    return;
  }

  // 点击在弹窗上
  if (
    hasAncestorWithClass(event.target, "el-overlay-dialog") ||
    hasAncestorWithClass(event.target, "el-overlay")
  ) {
    return;
  }

  // 点击在右侧面板上
  if (hasAncestorWithId(event.target, "panel")) {
    return;
  }
  // 点击在工具栏上
  if (hasAncestorWithId(event.target, "tools")) {
    return;
  }
  // 点击在组件外
  if (!el.contains(event.target)) {
    // 如果内容为空，显示确认对话框
    if (!props.content || props.content.trim().length == 0) {
      ElMessageBox.confirm("当前内容为空后不可再次编辑", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        customClass: "junzhiMessage",
        type: "warning",
      })
        .then(() => {
          // 用户点击确定，退出编辑模式
          operatingTextIndex.value = -1;
          exitEditMode();
        })
        .catch(() => {
          // 用户点击取消，不做任何操作，保持编辑模式
          setTimeout(() => {
            tiptapRef.value?.context?.editor?.value?.chain()?.focus()?.run();
          }, 100);
          return;
        });
    } else {
      // 内容不为空，直接退出编辑模式
      operatingTextIndex.value = -1;
      exitEditMode();
    }
  }
};

// 退出编辑模式的函数
const exitEditMode = () => {
  setAllItemsToViewMode();
  popType.value = false;
};

// 判断一个元素是否拥有一个id为ancestorId的祖先
const hasAncestorWithId = (element, ancestorId) => {
  let currentElement = element;

  while (currentElement) {
    // Check if the current element's ID matches the ancestorId
    if (currentElement.id === ancestorId) {
      return true;
    }
    // Move to the parent element
    currentElement = currentElement.parentElement;
  }

  return false;
};

// 判断一个元素是否拥有一个class中包含ancestorClass的祖先
const hasAncestorWithClass = (element, ancestorClass) => {
  let currentElement = element;

  while (currentElement) {
    // Check if the current element's ID matches the ancestorId
    if (currentElement.classList.contains(ancestorClass)) {
      return true;
    }
    // Move to the parent element
    currentElement = currentElement.parentElement;
  }

  return false;
};
watch(
  () => props.mode,
  () => {
    if (props.mode === "edit") {
      nextTick(() => {
        clickListener = handleOutsideClick;
        document.addEventListener("click", clickListener);
      });
    } else {
      if (clickListener) {
        document.removeEventListener("click", clickListener);
        clickListener = null;
        currentEditor.value = null;
      }
    }
  },
  { immediate: true }
);
onUnmounted(() => {
  if (clickListener) {
    document.removeEventListener("click", clickListener);
  }
  if (currentEditor.value) {
    currentEditor.value = null;
  }
});
</script>

<style lang="scss" scoped>
.paragraph-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px 10px;
  border: 0;
  border-radius: 8px;
}

// .paragraph-container:not(.is-busy):not(.is-child):not(.edit):hover {
//   background-color: #f2f3f7;
//   border-radius: 4px;
//   cursor: pointer;
// }
.nochild {
  padding: 0 8px;
}
.paragraph-container:not(.is-busy).edit > .nochild:hover {
  // background-color: #f2f3f7;
  border-radius: 4px;
  cursor: pointer;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  .title-container {
    flex: 1;

    .title {
      font-weight: bold;
      font-size: 16px;
      cursor: default;

      &.editable {
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: background-color 0.2s;

        &:hover {
          background-color: rgba(106, 246, 255, 0.1);
        }
      }
    }

    .title-input {
      font-weight: bold;
      font-size: 16px;

      :deep(.el-input__inner) {
        font-weight: bold;
        font-size: 16px;
        border-radius: 4px;
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;

    .action-btn {
      width: 28px;
      height: 28px;
      border-radius: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #6af6ff;
      transition: all 0.2s;

      &:hover {
        background-color: rgba(106, 246, 255, 0.1);
        transform: scale(1.1);
      }

      &.delete-btn {
        color: #f56c6c;

        &:hover {
          background-color: rgba(245, 108, 108, 0.1);
        }
      }

      i {
        font-size: 14px;
      }
    }
  }
}
.iconChat2 {
  display: block;
  text-align: right;
  padding: 5px 10px;
  color: #6af6ff;
  cursor: pointer;
  float: right;
}

.icon {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.content {
  font-size: 14px;
  line-height: 1.5;
  color: #4b5563;
}

.markdown-cnt {
  line-height: 22px;
  font-size: 14px;
}

.markdown-cnt:deep(ul) {
  list-style: disc;
}

.markdown-cnt:deep(li) {
  margin: 8px 0;
}

.content:deep(.toastui-editor-mode-switch) {
  display: none !important;
}
.think-con {
  padding: 0 10px;
  .think-title {
    color: #6af6ff;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 6px;
  }
  .think-content {
    color: #a0c7da;
    // color: #426777;
    // :deep(blockquote) {
    //   color: #a0c7da;
    //   margin: 0;
    // }
    // :deep(h2) {
    //   color: #426777;
    //   font-size: 18px;
    //   font-weight: bold;
    // }
  }
}
.collapseBox {
  :deep(.el-collapse-item__header) {
    color: #6af6ff;
  }
}
.briefStyle {
  background-color: transparent;
  border-radius: 10px;
  border: 1px solid #157d95;
  :deep(.ept-toolbar-btn) {
    color: #999;
  }
}
</style>
