<template>
  <div class="design-form">
    <el-scrollbar ref="scrollbarRef">
      <div class="chat-section">
        <message-list
          :message-list="list"
          :enable-buttons="true"
          :is-loading="false"
          :enable-auto-scroll="true"
          :fn-btn-list="
            (msg) => {
              return msg.type === 'answer'
                ? ['insert', 'copy', 'regenerate']
                : [];
            }
          "
          @regenerate="handleRegenerate"
          @insert="handleInsert"
        />
      </div>
    </el-scrollbar>

    <div class="action-section">
      <ask
        :max-chars="500"
        :is-data-bank="false"
        :is-responsing="isResponsing"
        @ask="handleAsk"
        @stop="handleStop"
        class="ask"
      />
    </div>
  </div>
</template>

<script setup>
import {
  ref,
  computed,
  defineProps,
  nextTick,
  watch,
  unref,
  onUnmounted,
  onMounted,
} from "vue";
import Ask from "@/views/briefGeneration/chat/ask.vue";
import MessageList from "@/views/briefGeneration/chat/messageList.vue";
import ChatManager from '../../../chat/chatManager'
import { paragraphs, operatingTextIndex, currentEditor } from "../js/sharedState"; 

const scrollbarRef = ref(null);
const props = defineProps({
  selectedItem: {
    type: Object,
    required: false,
    default: {},
  },
});


const cmgr = new ChatManager()
cmgr.setRequestUrl('/proxy/chat')
cmgr.setFuncCreateParams(({question}) => {
  const fileUrls = []
  if (question.files) {
    question.files.forEach(item => {
      fileUrls.push(item.response.url)
    })
  }
  const curParagraph = getParagraph(operatingTextIndex.value)
  return {
    'response mode': 'streaming',
    inputs: JSON.stringify({
      content: curParagraph.content,
      file_content: fileUrls,
      is_knowledge: question.knowledge ? 'true' : 'false',
      web_search: question.network ? 'true' : 'false'
    }),
    query: question.cnt,
    appSecretKey: 'report_polish',
    isUpload: question.files && question.files.length > 0 ? 1 : 0
  }
})
cmgr.setPrologue('我是您的AI助理，有什么问题我可以帮到您的话可以尽管提问哦～')
const list = cmgr.messageList
const isResponsing = cmgr.isResponsing

function handleAsk(question) {
  cmgr.send(question)
}

function handleStop() {
  cmgr.stop()
}

function handleRegenerate(idx) {
  cmgr.regenerate(idx)
}

function handleInsert(idx) {
  const msg = cmgr.messageList.value[idx].content
  currentEditor.value.chain().focus().insertContent(msg).run()
}

function getParagraph(idx) {
  if (typeof idx === 'number') {
    return paragraphs.value[idx]
  }

  if (typeof idx === 'string' && !idx.includes('-')) {
    return paragraphs.value[parseInt(idx)]
  }

  if (typeof idx === 'string' && idx.includes('-')) {
    const tmps = idx.split('-')
    const menuIndex = parseInt(tmps[0])
    const midx = parseInt(tmps[1])
    const sidx = parseInt(tmps[2])
    const tiidx = parseInt(tmps[3])
    if(tmps.length === 4) {
      return paragraphs.value[menuIndex][midx].subSections[sidx].subSections[tiidx]
    }else if(tmps.length === 3) {
      return paragraphs.value[menuIndex][midx].subSections[sidx]
    }else if(tmps.length === 2) {
      return paragraphs.value[menuIndex][midx]
    }
  }

  return null
}

// 卸载时取消所有问答接口
onUnmounted(() => {
  cmgr.stop()
});
</script>

<style lang="scss" scoped>
.design-form {
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  height: 100%;
  overflow: hidden;
  width: 396px;
}

.chat-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  flex-shrink: 0;
  overflow: auto;
  flex-basis: 200px;
  padding: 16px;

  &::v-deep .message-container.answering .message,
  &::v-deep .message-container.answer .message {
    background: #1D5974;
    border-radius: 0px 8px 8px 8px;
    max-width: calc(100% - 32px);
    padding: 12px;
    font-size: 12px !important;
    line-height: 20px !important;
  }

  &::v-deep .message-container.question .message {
    background: #173567;
    border-radius: 8px 8px 0 8px;
    max-width: calc(100% - 32px);
    color: #fff;
  }

  &::v-deep .message-container.prologue .message {
    background: #1D5974;
    border-radius: 0px 8px 8px 8px;
    max-width: calc(100% - 32px);
    color: #fff;
  }

  .link-insert-container {
    width: 100%;
    border: 0;
    border-top: 1px solid #e4e4e4;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding-top: 7px;
    .link-insert-wrapper {
      cursor: pointer;
      font-size: 18px;
      color: #848484;
      &:not(:last-child) {
        margin-right: 10px;
      }
    }
  }
}

.action-section {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-start;
  flex-grow: 0;
  flex-shrink: 0;
  flex-basis: 94px;
  height: 248px;
  padding: 16px;

  .ask {
    margin-top: 16px;
  }

  .switch {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;

    .text {
      font-size: 12px;
      color: #848691;
      margin-right: 14px;
    }
  }

  .btn {
    width: 294px;
    height: 32px;
    margin-top: 16px;
  }
}
.result-content {
  border-radius: 0;
  margin-bottom: 20px;
  .loadingBox {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .loadText {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #848691;
      line-height: 12px;
      font-style: normal;
      margin-top: 10px;
    }
  }
  .items {
    max-height: 188px;
    overflow: auto;
    // display: flex;
    // flex-direction: column;
    // justify-content: flex-start;
    // align-items: flex-start;
    padding: 12px;
    background: #e9f1fe;
    border-radius: 8px;
    // border: 1px solid rgba(42, 116, 249, 0.1);
    // font-size: 14px;
    // line-height: 24px;
    border: 1px solid #2a74f9;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #1e1f24;
    line-height: 18px;
    .tips {
      display: inline;
      font-weight: 600;
    }
    span {
      // color: #2a74f9;
      // &::before {
      //   display: inline;
      //   content: "\6587\7ae0\4e3b\8981\4fe1\606f\ff1a";
      //   font-weight: 600;
      // }
    }
  }
}
</style>
