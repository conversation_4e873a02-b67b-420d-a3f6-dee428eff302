<template>
  <div class="design-page">
    <div class="content" ref="contentRef">
      <template v-if="paragraphs.length === 0">
        <div
          style="
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #fff;
            line-height: 14px;
            margin: 0 atuo;
            text-align: center;
            padding-top: 50px;
          "
        >
          {{ startGenerating ? "正在为你生成简报..." : "暂无内容" }}
        </div>
      </template>
      <div ref="scrollRef">
        <template
          v-for="(paragraphsItem, paragraphsIndex) in paragraphs"
          :key="paragraphsIndex"
        >
          <Paragraph
            v-for="(item, index) in paragraphsItem"
            :key="index"
            :nodeId="`section${paragraphsIndex}-${index}`"
            :parentIndex="paragraphsIndex"
            :title="item.title"
            :index="`${paragraphsIndex}-${index}`"
            :mode="item.mode"
            :think="item.think"
            :type="item.type"
            v-model:content="item.content"
            v-model:subSections="item.subSections"
            :collapse-status="collapseStatus"
            @update:title="(idx, newTitle) => handleTitleUpdate(idx, newTitle)"
            @add-sibling="handleAddSibling"
            @add-child="handleAddChild"
            @delete-section="handleDeleteSection"
            :readonly="pageType === 'view'"
          >
          </Paragraph>
        </template>
      </div>
    </div>
    <competitorDialog />

    <div
      class="btnBg"
      title="终止生成"
      @click="stop"
      :style="{ cursor: startGenerating ? 'pointer' : 'not-allowed' }"
      v-show="pageType == 'add' && startGenerating"
    >
      <i class="ri-stop-fill"></i>
    </div>
  </div>
</template>

<script setup>
import { ref } from "@vue/reactivity";
import { ElMessageBox, ElMessage } from "element-plus";
import Paragraph from "./paragraph.vue";
import { sseRequest } from "@/utils/request.js";
import {
  paragraphs,
  // saveParagraphs,
  startGenerating,
  operatingTextIndex,
  menuList,
  scrollRef,
  // form,
  pageType,
  pageId,
  configData,
  ishumanInput,
  competitorParams,
  top5Options,
} from "@/views/briefGeneration/add/create/js/sharedState.js";
import competitorDialog from "./competitorDialog.vue";
import { useRoute, useRouter } from "vue-router";
import { onUnmounted, onMounted, nextTick } from "vue";
import { getCatalogList } from "@/api/briefGeneration/catalog.js";
import { getAppSecretKey } from "@/api/briefGeneration/index.js";
import Md from "@/components/Markdown";

const allowSegmentation = ref(false);
const route = useRoute();
const generatedTaskId = ref("");
const generatedCtrl = ref(null);
const router = useRouter();
const collapseStatus = reactive({});
// 定义content的引用
const contentRef = ref(null);

const appSecretKeyList = ref([]);

function scrollToBottom() {
  // 滚动到底部
  if (contentRef.value) {
    contentRef.value.scrollTop = contentRef.value.scrollHeight;
  }
}

let content = "";
function processMarkdownContent(markdownContent) {
  const lines = markdownContent.split("\n");
  // 不清空paragraphs，而是从lastParagraphCount开始处理新内容
  // paragraphs.value = []; // 注释掉这行，保留之前的数据

  // 移除从lastParagraphCount开始的所有段落，然后重新处理当前content
  paragraphs.value[workflowIndex.value] = [];

  let currentSection = null; // 一级菜单
  let currentSubSection = null; // 二级菜单
  let currentThirdSection = null; // 三级菜单
  let currentFourSection = null; // 四级菜单
  let inThink = false;
  let currentIndex = -1;
  let contentIndex = -1;

  lines.forEach((line) => {
    if (line.includes("<think>")) {
      contentIndex = -1;
      inThink = true;
      paragraphs.value[workflowIndex.value].push({
        title: "",
        think: "",
        mode: "view",
        type: "think",
        subSections: [],
      });
      currentIndex = paragraphs.value[workflowIndex.value].length - 1;
      let afterTag = line.substring(line.indexOf("<think>") + 7);
      // 使用全局索引来生成nodeId，确保ID的连续性
      const nodeId = `section${workflowIndex.value}-${currentIndex}`;
      collapseStatus[nodeId] = ["collapse"];
      if (afterTag.includes("</think>")) {
        paragraphs.value[workflowIndex.value][currentIndex].think =
          (paragraphs.value[workflowIndex.value][currentIndex].think || "") +
          afterTag.substring(0, afterTag.indexOf("</think>"));
        inThink = false;
        collapseStatus[nodeId] = [];
      } else {
        paragraphs.value[workflowIndex.value][currentIndex].think =
          (paragraphs.value[workflowIndex.value][currentIndex].think || "") +
          afterTag;
      }
      return;
    }

    // 2. <think> 内内容
    if (inThink) {
      contentIndex = -1;
      // 使用全局索引来生成nodeId，确保ID的连续性
      const nodeId = `section${workflowIndex.value}-${currentIndex}`;
      if (line.includes("</think>")) {
        paragraphs.value[workflowIndex.value][currentIndex].think =
          (paragraphs.value[workflowIndex.value][currentIndex].think || "") +
          "\n" +
          line.substring(0, line.indexOf("</think>"));
        inThink = false;
        collapseStatus[nodeId] = [];
      } else {
        paragraphs.value[workflowIndex.value][currentIndex].think =
          (paragraphs.value[workflowIndex.value][currentIndex].think || "") +
          "\n" +
          line;
      }
      return;
    }

    if (/^# \s*/.test(line)) {
      contentIndex = -1;
      // 一级菜单，单独一条数据
      const title = line.replace(/^#\s*/, "");
      let thinkContent = "";
      if (currentIndex !== -1) {
        thinkContent =
          paragraphs.value[workflowIndex.value][currentIndex].think.trim();
        paragraphs.value[workflowIndex.value].splice(currentIndex, 1);
        currentIndex = -1;
      }
      currentSection = {
        title,
        think: thinkContent,
        content: null,
        mode: "view",
        type: 1,
        subSections: [],
      };
      paragraphs.value[workflowIndex.value].push(currentSection);
      // 关键：一级菜单后，二级、三级都不再归属于它
      // currentSection = null;
      currentSubSection = null;
      currentThirdSection = null;
      currentFourSection = null;
      return;
    } else if (/^## \s*/.test(line)) {
      contentIndex = -1;
      // 二级菜单，直接 push 到顶层
      const title = line.replace(/^##\s*/, "");
      let thinkContent = "";
      if (currentIndex !== -1) {
        thinkContent =
          paragraphs.value[workflowIndex.value][currentIndex].think.trim();
        paragraphs.value[workflowIndex.value].splice(currentIndex, 1);
        currentIndex = -1;
      }
      currentSubSection = {
        title,
        think: thinkContent,
        content: null,
        mode: "view",
        type: 2,
        subSections: [],
      };
      paragraphs.value[workflowIndex.value].push(currentSubSection);
      currentThirdSection = null;
      currentFourSection = null;
      return;
    } else if (/^### \s*/.test(line)) {
      contentIndex = -1;
      // 三级菜单，还是作为二级的子级
      const subTitle = line.replace(/^###\s*/, "");
      let thinkContent = "";
      if (currentIndex !== -1) {
        thinkContent =
          paragraphs.value[workflowIndex.value][currentIndex].think.trim();
        paragraphs.value[workflowIndex.value].splice(currentIndex, 1);
        currentIndex = -1;
      }
      currentThirdSection = {
        title: subTitle,
        think: thinkContent,
        content: null,
        type: 3,
        mode: "view",
        subSections: [],
      };
      if (currentSubSection) {
        currentSubSection.subSections.push(currentThirdSection);
      } else {
        // 没有二级菜单时，直接挂到顶层
        paragraphs.value[workflowIndex.value].push(currentThirdSection);
      }
      currentFourSection = null;
      return;
    } else if (/^#### \s*/.test(line)) {
      contentIndex = -1;
      // 四级菜单，还是作为三级的子级
      const subTitle = line.replace(/^####\s*/, "");
      let thinkContent = "";
      if (currentIndex !== -1) {
        thinkContent =
          paragraphs.value[workflowIndex.value][currentIndex].think.trim();
        paragraphs.value[workflowIndex.value].splice(currentIndex, 1);
        currentIndex = -1;
      }
      currentFourSection = {
        title: subTitle,
        think: thinkContent,
        content: null,
        type: 4,
        mode: "view",
      };
      if (currentThirdSection) {
        currentThirdSection.subSections.push(currentFourSection);
      } else if (currentSubSection) {
        currentSubSection.subSections.push(currentFourSection);
      } else {
        // 没有二级菜单时，直接挂到顶层
        paragraphs.value[workflowIndex.value].push(currentFourSection);
      }
      return;
    } else {
      // 有标题的情况 - 保留所有行（包括空行）
      if (currentFourSection) {
        contentIndex = -1;
        currentFourSection.content =
          (currentFourSection.content || "") + line + "\n";
      } else if (currentThirdSection) {
        contentIndex = -1;
        currentThirdSection.content =
          (currentThirdSection.content || "") + line + "\n";
      } else if (currentSubSection) {
        contentIndex = -1;
        currentSubSection.content =
          (currentSubSection.content || "") + line + "\n";
      } else if (currentSection) {
        currentSection.content = (currentSection.content || "") + line + "\n";
      } else {
        // 无标题的情况
        if (contentIndex == -1) {
          let thinkContent = "";
          if (currentIndex !== -1) {
            thinkContent =
              paragraphs.value[workflowIndex.value][currentIndex].think.trim();
            paragraphs.value[workflowIndex.value].splice(currentIndex, 1);
            currentIndex = -1;
          }
          paragraphs.value[workflowIndex.value].push({
            title: "",
            think: thinkContent,
            content: null,
            type: null,
            mode: "view",
          });
        }
        contentIndex = paragraphs.value[workflowIndex.value].length - 1;
        paragraphs.value[workflowIndex.value][contentIndex].content =
          (paragraphs.value[workflowIndex.value][contentIndex].content || "") +
          line +
          "\n";
      }
    }
  });
}
const onGeneratingMessage = (event) => {
  if (!startGenerating.value) {
    return;
  }
  const data = event.data && JSON.parse(event.data);
  generatedTaskId.value = data.task_id;

  if (data.event === "message" && data.answer) {
    // console.log("接收到了消息:", data);
    // 追加流式数据到content
    content += data.answer;
    // console.log("content :>> ", JSON.stringify(content));
    processMarkdownContent(content);
    // console.log(content); // 打印当前content内容
    // paragraphs.value[0].content = content;
  }
  //人工介入
  if (
    data.event == "node_finished" &&
    data.data.node_type == "human-input-text"
  ) {
    competitorParams.value.node_id = data.data.node_id;
    competitorParams.value.conversation_id = data.conversation_id;
    top5Options.value = data.data.inputs.question.split(";").slice(0, 5);
    //展示输入选择框
    ishumanInput.value = true;
  }

  scrollToBottom();
};

// 定义关闭事件的处理函数
const onClose = (index) => {
  console.log(`第 ${index + 1} 个菜单项的 SSE 连接已关闭`);

  generatedTaskId.value = "";
  if (content && allowSegmentation.value) {
    allowSegmentation.value = false;

    // 生成左侧目录 menuList，使用当前index对应的menuList项
    if (menuList.value[index]) {
      const extractedSections = extractType2Sections(
        paragraphs.value[workflowIndex.value]
      );
      menuList.value[index].children = extractedSections;
      console.log(
        `为菜单项 ${
          menuList.value[index].name || menuList.value[index].id
        } 更新了 ${extractedSections.length} 个子目录`
      );
    } else {
      console.warn(`菜单项索引 ${index} 不存在，无法更新子目录`);
    }
  }
  console.log("paragraphs.value :>> ", content, paragraphs.value);
};
function extractType2Sections(paragraphs = []) {
  let arr = paragraphs
    .map((p, idx) => ({
      id: `/section${workflowIndex.value}-${idx}`,
      name: p.title || `段落${idx + 1}`,
      type: p.type,
      children: (p.subSections || [])
        .map((sub, subIdx) => ({
          id: `/section${workflowIndex.value}-${idx}-${subIdx}`,
          name: sub.title || `子段落${subIdx + 1}`,
          type: sub.type,
        }))
        .filter((sub) => sub.type == 3),
    }))
    .filter((p) => p.type == 2);
  return arr || [];
}
// 定义错误事件的处理函数
const onError = (error) => {
  content = "";
  console.error("SSE 出现错误:重连并清空本次content", error);
};
const workflowIndex = ref(0);
// 记录每轮开始前的段落数

function startWorkflowStream(index = 0) {
  console.log(
    `开始处理第 ${index + 1} 个菜单项，总共 ${menuList.value.length} 个`
  );

  const currentMenuItem = menuList.value[index];

  // 验证菜单项
  if (!validateMenuItem(index, currentMenuItem)) {
    startGenerating.value = false;
    return;
  }

  console.log(`正在处理菜单项: ${currentMenuItem.name || currentMenuItem.id}`);

  // 重置内容和状态
  content = "";
  console.log(`重置content，开始新的内容生成`);
  generatedCtrl.value = new AbortController();

  // 根据当前menuList项的id获取对应的appSecretKey
  const appSecretKey = getAppSecretKeyById(currentMenuItem.id);
  console.log(
    `使用 appSecretKey: ${appSecretKey} 为菜单项 ${currentMenuItem.id} 生成内容`
  );

  // 设置生成状态
  allowSegmentation.value = true;
  const touchesObj = {
    response_mode: "streaming",
    inputs: JSON.stringify({
      brand: configData.value.brandName || "水星家纺",
    }),
    query: configData.value.title || "生成简报",
    appSecretKey: appSecretKey,
  };
  sseRequest(
    "/proxy/chat",
    touchesObj,
    onGeneratingMessage,
    () => {
      onClose(index);
      startWorkflowStream(index + 1);
      workflowIndex.value = index + 1;
    },
    onError,
    generatedCtrl.value.signal
  );
}
function stop() {
  if (startGenerating) {
    generatedCtrl.value && generatedCtrl.value.abort(); // 终止请求（可选）
    startGenerating.value = false;
    generatedTaskId.value = "";
    // 使用当前workflowIndex对应的menuList项
    if (
      menuList.value[workflowIndex.value] &&
      paragraphs.value[workflowIndex.value].length > 0
    ) {
      menuList.value[workflowIndex.value].children = extractType2Sections(
        paragraphs.value[workflowIndex.value]
      );
    }
  }
}
watch(startGenerating, (newValue) => {
  if (newValue) {
    workflowIndex.value = 0;
    startWorkflowStream(workflowIndex.value);
  }
});
const getMenuList = async (params) => {
  const res = await getCatalogList(params);
  if (res.code === 200) {
    menuList.value = res.data || [];
  } else {
    return [];
  }
};
const getKeyList = async (params) => {
  const res = await getAppSecretKey(params);
  if (res.code === 200) {
    appSecretKeyList.value = res.data || [];
  } else {
    return [];
  }
};
const init = async () => {
  // 重置数据
  menuList.value = [];
  appSecretKeyList.value = [];
  paragraphs.value = [];
  operatingTextIndex.value = -1;

  try {
    // 获取appSecretKey配置列表
    await getKeyList({ labelConfigId: configData.value.labelConfigId });
    if (appSecretKeyList.value.length == 0) {
      ElMessage({
        type: "error",
        message: `没有对应目录，无法生成简报！为您返回列表页`,
        duration: 1000,
      });
      setTimeout(() => {
        router.push("/briefGeneration/list");
      }, 1000);
      return;
    }
    // 获取默认目录列表
    await getMenuList({
      structureVersion: configData.value.version,
      catalogIdList: appSecretKeyList.value.map((v) => v.catalogId).join(","),
    });
    console.log("初始化完成", {
      menuCount: menuList.value.length,
      keyCount: appSecretKeyList.value.length,
    });

    startGenerating.value = true;
  } catch (error) {
    console.log("初始化失败:", error);
    ElMessage.error("初始化失败，请重试");
  }
};
// 根据menuList的id获取对应的appSecretKey
function getAppSecretKeyById(id) {
  console.log(`查找 catalogId: ${id} 对应的 configKey`);

  // 在appSecretKeyList中查找对应catalogId的数据，返回其configKey
  const keyItem = appSecretKeyList.value.find((item) => item.catalogId === id);
  if (keyItem && keyItem.configKey) {
    console.log(`找到对应的 configKey: ${keyItem.configKey}`);
    return keyItem.configKey;
  }

  console.log(`未找到 catalogId: ${id} 对应的 configKey，使用默认值`);

  // 如果没有找到对应的配置，根据在menuList中的索引生成默认的appSecretKey
  const menuIndex = menuList.value.findIndex((item) => item.id === id);
  const defaultKey = menuIndex >= 0 ? `workflow${menuIndex + 1}` : `workflow1`;

  console.log(`使用默认 configKey: ${defaultKey}`);
  return defaultKey;
}

// 验证菜单项
function validateMenuItem(index, menuItem) {
  if (index >= menuList.value.length) {
    console.log("所有菜单项处理完成，停止生成");
    return false;
  }

  if (!menuItem) {
    console.log(`第 ${index} 个菜单项不存在，停止生成`);
    return false;
  }

  return true;
}

// 处理标题更新
const handleTitleUpdate = (index, newTitle) => {
  if (typeof index === "string" && index.includes("-")) {
    const indexParts = index.split("-");
    if (indexParts.length === 2) {
      // 二级标题 (menuIndex-paragraphIndex) - 正式段落
      const menuIndex = parseInt(indexParts[0]);
      const paragraphIndex = parseInt(indexParts[1]);
      if (
        paragraphs.value[menuIndex] &&
        paragraphs.value[menuIndex][paragraphIndex]
      ) {
        paragraphs.value[menuIndex][paragraphIndex].title = newTitle;
      }
    } else if (indexParts.length === 3) {
      // 三级标题 (menuIndex-paragraphIndex-subIndex)
      const menuIndex = parseInt(indexParts[0]);
      const paragraphIndex = parseInt(indexParts[1]);
      const subIndex = parseInt(indexParts[2]);
      if (
        paragraphs.value[menuIndex] &&
        paragraphs.value[menuIndex][paragraphIndex] &&
        paragraphs.value[menuIndex][paragraphIndex].subSections[subIndex]
      ) {
        paragraphs.value[menuIndex][paragraphIndex].subSections[
          subIndex
        ].title = newTitle;
      }
    } else if (indexParts.length === 4) {
      // 四级标题 (menuIndex-paragraphIndex-subIndex-thirdIndex)
      const menuIndex = parseInt(indexParts[0]);
      const paragraphIndex = parseInt(indexParts[1]);
      const subIndex = parseInt(indexParts[2]);
      const thirdIndex = parseInt(indexParts[3]);
      if (
        paragraphs.value[menuIndex] &&
        paragraphs.value[menuIndex][paragraphIndex] &&
        paragraphs.value[menuIndex][paragraphIndex].subSections[subIndex] &&
        paragraphs.value[menuIndex][paragraphIndex].subSections[subIndex]
          .subSections[thirdIndex]
      ) {
        paragraphs.value[menuIndex][paragraphIndex].subSections[
          subIndex
        ].subSections[thirdIndex].title = newTitle;
      }
    }
  }
};

// 处理添加同级内容
const handleAddSibling = (index, type) => {
  const newSection = {
    title: "新建标题",
    content: "",
    mode: "edit",
    type,
    subSections: [],
    think: "",
    aa: "hh",
  };

  let newIndex;
  console.log("index :>> ", index, "type", type);
  if (typeof index === "string" && index.includes("-")) {
    const indexParts = index.split("-");
    const menuIndex = parseInt(indexParts[0]);
    const paragraphIndex = parseInt(indexParts[1]);
    const subIndex = parseInt(indexParts[2]);
    const thirdIndex = parseInt(indexParts[3]);
    if (indexParts.length === 2) {
      // 在二级段落中添加同级 (menuIndex-paragraphIndex)
      paragraphs.value[menuIndex]?.splice(paragraphIndex + 1, 0, newSection);
      newIndex = `${menuIndex}-${paragraphIndex + 1}`;
    } else if (indexParts.length === 3) {
      // 在三级中添加同级 (menuIndex-paragraphIndex-subIndex)
      paragraphs.value[menuIndex][paragraphIndex].subSections?.splice(
        subIndex + 1,
        0,
        newSection
      );
      newIndex = `${menuIndex}-${paragraphIndex}-${subIndex + 1}`;
    } else if (indexParts.length === 4) {
      // 在四级中添加同级 (menuIndex-paragraphIndex-subIndex-thirdIndex)
      paragraphs.value[menuIndex][paragraphIndex].subSections[
        subIndex
      ].subSections?.splice(thirdIndex + 1, 0, newSection);
      newIndex = `${menuIndex}-${paragraphIndex}-${subIndex}-${thirdIndex + 1}`;
    }
  }

  // 设置操作索引
  operatingTextIndex.value = newIndex;

  console.log("paragraphs.value :>> ", paragraphs.value);
};

// 处理添加子级内容
const handleAddChild = (index, type) => {
  const newSection = {
    title: "新建子标题",
    content: "",
    mode: "edit",
    type,
    subSections: [],
    think: "",
  };

  let newIndex;
  console.log("index :>> ", index, "type", type);
  if (typeof index === "string" && index.includes("-")) {
    const indexParts = index.split("-");
    if (indexParts.length === 2) {
      // 在二级段落中添加子级（三级） (menuIndex-paragraphIndex)
      const menuIndex = parseInt(indexParts[0]);
      const paragraphIndex = parseInt(indexParts[1]);
      if (!paragraphs.value[menuIndex][paragraphIndex].subSections) {
        paragraphs.value[menuIndex][paragraphIndex].subSections = [];
      }
      const newSubIndex =
        paragraphs.value[menuIndex][paragraphIndex].subSections.length;
      paragraphs.value[menuIndex][paragraphIndex].subSections.push(newSection);
      newIndex = `${menuIndex}-${paragraphIndex}-${newSubIndex}`;
    } else if (indexParts.length === 3) {
      // 在三级中添加子级（四级） (menuIndex-paragraphIndex-subIndex)
      const menuIndex = parseInt(indexParts[0]);
      const paragraphIndex = parseInt(indexParts[1]);
      const subIndex = parseInt(indexParts[2]);
      if (
        !paragraphs.value[menuIndex][paragraphIndex].subSections[subIndex]
          .subSections
      ) {
        paragraphs.value[menuIndex][paragraphIndex].subSections[
          subIndex
        ].subSections = [];
      }
      const newThirdIndex =
        paragraphs.value[menuIndex][paragraphIndex].subSections[subIndex]
          .subSections.length;
      paragraphs.value[menuIndex][paragraphIndex].subSections[
        subIndex
      ].subSections.push(newSection);
      newIndex = `${menuIndex}-${paragraphIndex}-${subIndex}-${newThirdIndex}`;
    }
  }

  // 设置操作索引
  operatingTextIndex.value = newIndex;

  // 激活新添加内容的编辑模式
};

// 处理删除内容
const handleDeleteSection = (index) => {
  if (typeof index === "string" && index.includes("-")) {
    const indexParts = index.split("-");
    if (indexParts.length === 2) {
      // 删除二级段落 (menuIndex-paragraphIndex)
      const menuIndex = parseInt(indexParts[0]);
      const paragraphIndex = parseInt(indexParts[1]);
      paragraphs.value[menuIndex].splice(paragraphIndex, 1);
    } else if (indexParts.length === 3) {
      // 删除三级 (menuIndex-paragraphIndex-subIndex)
      const menuIndex = parseInt(indexParts[0]);
      const paragraphIndex = parseInt(indexParts[1]);
      const subIndex = parseInt(indexParts[2]);
      paragraphs.value[menuIndex][paragraphIndex].subSections.splice(
        subIndex,
        1
      );
    } else if (indexParts.length === 4) {
      // 删除四级 (menuIndex-paragraphIndex-subIndex-thirdIndex)
      const menuIndex = parseInt(indexParts[0]);
      const paragraphIndex = parseInt(indexParts[1]);
      const subIndex = parseInt(indexParts[2]);
      const thirdIndex = parseInt(indexParts[3]);
      paragraphs.value[menuIndex][paragraphIndex].subSections[
        subIndex
      ].subSections.splice(thirdIndex, 1);
    }
  }
};

onMounted(async () => {
  pageType.value = route.params.type || "add";
  pageId.value = route.params.id;
  if (pageType.value === "add") {
    // const saved = localStorage.getItem("myForm");
    const saved = localStorage.getItem("configData");
    if (saved) {
      // form.value = JSON.parse(saved);
      configData.value = { ...configData.value, ...JSON.parse(saved) };
      init();
    } else {
      ElMessage({
        type: "error",
        message: "当前未配置简报项",
        duration: 300,
      });
      setTimeout(() => {
        router.push("/briefGeneration/list");
      }, 300);
    }
  }
});
onUnmounted(() => {
  startGenerating.value = false;
  generatedTaskId.value = "";
  generatedCtrl.value && generatedCtrl.value.abort(); // 终止请求（可选）
});
</script>

<style lang="scss" scoped>
.design-page {
  position: relative;
  height: 100%;
  color: #d6f8ff;
  .btnBg {
    position: absolute;
    bottom: 50px;
    right: 100px;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(
      136.1deg,
      rgba(75, 175, 247, 1) -17.4%,
      rgba(9, 60, 98, 1) 100%
    );
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: #ffffff;
    cursor: pointer;
    &:hover {
      box-shadow: 1px 0px 2px 0px rgba(0, 229, 255, 1);
      border: 1px solid rgba(0, 229, 255, 1);
    }
  }
}

.content {
  // width: 798px;
  // margin: 0 auto;
  // padding: 17px 25px;
  max-height: 100%;
  overflow-y: auto;
  position: relative;
  min-height: 500px;
  .think-con {
    padding: 0 10px;
  }
  .think-title {
    color: #515151;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 6px;
  }
  .think-content {
    color: #a0c7da;
    // color: #426777;
    // :deep(blockquote) {
    //   color: #a0c7da;
    //   margin: 0;
    // }
    // :deep(h2) {
    //   color: #426777;
    //   font-size: 18px;
    //   font-weight: bold;
    // }
  }
}

// 固定Toolbar
// .design-page Toolbar {
//   position: fixed;
//   top: 0;
//   width: 100%;
//   background-color: white;
//   z-index: 1000;
// }
:deep(.collapseBox) {
  // .el-collapse-item__header,
  // .el-collapse-item__wrap {
  //   background-color: transparent;
  // }
  --el-collapse-border-color: transparent;
  --el-collapse-header-bg-color: transparent;
  --el-collapse-content-bg-color: transparent;
}
</style>
