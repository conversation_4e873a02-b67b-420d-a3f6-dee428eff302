<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML转文本功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        .test-input {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            margin-bottom: 10px;
        }
        .test-output {
            background-color: #e8f5e8;
            padding: 10px;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>HTML转文本功能测试</h1>
    <p>测试新的HTML转文本功能：div、p标签提取文本保留换行，a、img、table标签保持HTML格式</p>

    <div class="test-section">
        <h2>测试用例1：基础混合内容</h2>
        <div class="test-input" id="test1-input">
            <div class="content">
                <h1>标题内容</h1>
                <p>这是第一个段落</p>
                <p>这是包含<a href="https://example.com">链接</a>的段落</p>
                <div>这是div中的文本</div>
            </div>
        </div>
        <button onclick="runTest('test1')">转换测试1</button>
        <div class="test-output" id="test1-output"></div>
    </div>

    <div class="test-section">
        <h2>测试用例2：表格和图片</h2>
        <div class="test-input" id="test2-input">
            <div class="section">
                <p>数据表格：</p>
                <table border="1">
                    <tr>
                        <th>姓名</th>
                        <th>年龄</th>
                    </tr>
                    <tr>
                        <td>张三</td>
                        <td>25</td>
                    </tr>
                </table>
                <p>图片示例：</p>
                <img src="test.jpg" alt="测试图片" width="100" height="100">
            </div>
        </div>
        <button onclick="runTest('test2')">转换测试2</button>
        <div class="test-output" id="test2-output"></div>
    </div>

    <div class="test-section">
        <h2>测试用例3：复杂嵌套结构</h2>
        <div class="test-input" id="test3-input">
            <div class="container">
                <h2>章节标题</h2>
                <div class="content">
                    <p>段落1包含<a href="#link1">内部链接</a>和普通文字</p>
                    <div class="subsection">
                        <span>span中的内容</span>
                        <br>
                        <p>换行后的段落</p>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr><th>列1</th><th>列2</th></tr>
                        </thead>
                        <tbody>
                            <tr><td>数据1</td><td>数据2</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <button onclick="runTest('test3')">转换测试3</button>
        <div class="test-output" id="test3-output"></div>
    </div>

    <script>
        // 模拟 htmlToFormattedText 函数
        function htmlToFormattedText(htmlString, options = {}) {
            if (!htmlString) return '';

            const defaultOptions = {
                preserveLineBreaks: true,
                removeExtraSpaces: true,
                keepHtmlTags: ['a', 'img', 'table', 'thead', 'tbody', 'tr', 'td', 'th'],
                textOnlyTags: ['div', 'p', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
                addLineBreakTags: ['div', 'p', 'br', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']
            };

            const config = { ...defaultOptions, ...options };
            
            // 创建临时DOM元素
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlString;

            const result = [];

            // 递归处理所有子节点
            function processNode(node) {
                if (node.nodeType === Node.TEXT_NODE) {
                    const text = node.textContent || '';
                    if (text.trim()) {
                        result.push(text);
                    }
                    return;
                }

                if (node.nodeType === Node.ELEMENT_NODE) {
                    const tagName = node.tagName.toLowerCase();

                    // 需要保持HTML格式的标签
                    if (config.keepHtmlTags.includes(tagName)) {
                        result.push(node.outerHTML);
                        return;
                    }

                    // 只提取文本的标签
                    if (config.textOnlyTags.includes(tagName)) {
                        const textContent = node.textContent || node.innerText || '';
                        if (textContent.trim()) {
                            result.push(textContent);
                            
                            // 如果是需要添加换行的标签，添加换行符
                            if (config.addLineBreakTags.includes(tagName)) {
                                result.push('\n');
                            }
                        }
                        return;
                    }

                    // br 标签特殊处理
                    if (tagName === 'br') {
                        result.push('\n');
                        return;
                    }

                    // 其他标签递归处理子节点
                    for (let child of node.childNodes) {
                        processNode(child);
                    }
                }
            }

            // 处理所有子节点
            for (let child of tempDiv.childNodes) {
                processNode(child);
            }

            let text = result.join('');

            // 后处理
            if (config.preserveLineBreaks) {
                text = text.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
                text = text.replace(/\n{3,}/g, '\n\n');
                text = text.replace(/^\s+|\s+$/gm, '');
            }

            if (config.removeExtraSpaces) {
                text = text.replace(/[ \t]+/g, ' ');
            }

            return text.trim();
        }

        function runTest(testId) {
            const inputElement = document.getElementById(testId + '-input');
            const outputElement = document.getElementById(testId + '-output');
            
            const htmlContent = inputElement.innerHTML;
            const result = htmlToFormattedText(htmlContent);
            
            outputElement.textContent = result;
            
            console.log(`=== ${testId} 测试结果 ===`);
            console.log('输入HTML:', htmlContent);
            console.log('输出文本:', result);
        }

        // 页面加载完成后自动运行所有测试
        window.onload = function() {
            console.log('开始HTML转文本功能测试...');
            runTest('test1');
            runTest('test2');
            runTest('test3');
        };
    </script>
</body>
</html>
