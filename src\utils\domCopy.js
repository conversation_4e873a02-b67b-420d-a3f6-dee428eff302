/**
 * DOM复制工具函数
 * 模拟浏览器手动选中复制的效果
 */

/**
 * 复制DOM元素内容，保持原始格式（类似手动选中复制）
 * @param {HTMLElement} element - 要复制的DOM元素
 * @param {Object} options - 配置选项
 * @returns {Promise<boolean>} 复制是否成功
 */
export async function copyDomElement(element, options = {}) {
  if (!element) {
    throw new Error('没有可复制的元素');
  }

  const config = {
    includeStyles: true,      // 是否包含样式
    preserveFormatting: true, // 是否保持格式
    fallbackToText: true,     // 失败时是否降级到纯文本
    showMessage: true,        // 是否显示消息提示
    ...options
  };

  // 方法1: 使用Selection API（最接近手动复制）
  try {
    const success = await copyUsingSelection(element);
    if (success) {
      if (config.showMessage) {
        console.log('✅ 内容已复制到剪贴板（保持原始格式）');
      }
      return true;
    }
  } catch (error) {
    console.warn('Selection API 复制失败:', error);
  }

  // 方法2: 使用现代 Clipboard API
  try {
    const success = await copyUsingClipboardAPI(element, config);
    if (success) {
      if (config.showMessage) {
        console.log('✅ 内容已复制到剪贴板（包含格式）');
      }
      return true;
    }
  } catch (error) {
    console.warn('Clipboard API 复制失败:', error);
  }

  // 方法3: 降级到纯文本复制
  if (config.fallbackToText) {
    try {
      const textContent = element.innerText || element.textContent || '';
      await navigator.clipboard.writeText(textContent);
      if (config.showMessage) {
        console.log('✅ 内容已复制到剪贴板（纯文本）');
      }
      return true;
    } catch (error) {
      console.warn('纯文本复制失败:', error);
    }
  }

  throw new Error('所有复制方法都失败了');
}

/**
 * 使用Selection API复制（最接近手动选中复制）
 */
function copyUsingSelection(element) {
  return new Promise((resolve, reject) => {
    try {
      // 创建选择范围
      const range = document.createRange();
      const selection = window.getSelection();
      
      // 保存当前选择状态
      const originalRanges = [];
      for (let i = 0; i < selection.rangeCount; i++) {
        originalRanges.push(selection.getRangeAt(i));
      }
      
      // 清除现有选择
      selection.removeAllRanges();
      
      // 选择整个DOM元素内容
      range.selectNodeContents(element);
      
      // 添加到选择中
      selection.addRange(range);
      
      // 执行复制命令
      const successful = document.execCommand('copy');
      
      // 恢复原始选择状态
      selection.removeAllRanges();
      originalRanges.forEach(range => selection.addRange(range));
      
      if (successful) {
        resolve(true);
      } else {
        reject(new Error('execCommand copy 失败'));
      }
      
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 使用现代Clipboard API复制
 */
async function copyUsingClipboardAPI(element, config) {
  if (!navigator.clipboard || !navigator.clipboard.write) {
    throw new Error('Clipboard API 不支持');
  }

  try {
    // 获取HTML和文本内容
    const htmlContent = element.innerHTML;
    const textContent = element.innerText || element.textContent || '';
    
    // 创建剪贴板项目
    const clipboardItems = {};
    
    // 添加HTML格式
    if (config.preserveFormatting && htmlContent) {
      clipboardItems['text/html'] = new Blob([htmlContent], { type: 'text/html' });
    }
    
    // 添加纯文本格式
    if (textContent) {
      clipboardItems['text/plain'] = new Blob([textContent], { type: 'text/plain' });
    }
    
    // 写入剪贴板
    const clipboardItem = new ClipboardItem(clipboardItems);
    await navigator.clipboard.write([clipboardItem]);
    
    return true;
  } catch (error) {
    throw error;
  }
}

/**
 * 复制指定选择器的元素
 * @param {string} selector - CSS选择器
 * @param {Object} options - 配置选项
 */
export async function copyElementBySelector(selector, options = {}) {
  const element = document.querySelector(selector);
  if (!element) {
    throw new Error(`找不到选择器 "${selector}" 对应的元素`);
  }
  return copyDomElement(element, options);
}

/**
 * 复制多个元素的内容
 * @param {Array<HTMLElement>} elements - 元素数组
 * @param {Object} options - 配置选项
 */
export async function copyMultipleElements(elements, options = {}) {
  if (!elements || elements.length === 0) {
    throw new Error('没有可复制的元素');
  }

  // 创建临时容器
  const container = document.createElement('div');
  
  // 将所有元素的内容添加到容器中
  elements.forEach(element => {
    if (element && element.outerHTML) {
      container.innerHTML += element.outerHTML;
    }
  });

  try {
    const result = await copyDomElement(container, options);
    return result;
  } finally {
    // 清理临时容器
    container.remove();
  }
}

/**
 * 获取元素的完整HTML（包括样式）
 * @param {HTMLElement} element - DOM元素
 * @returns {string} 包含样式的HTML字符串
 */
export function getElementWithStyles(element) {
  if (!element) return '';

  // 获取元素的计算样式
  const computedStyle = window.getComputedStyle(element);
  const styleString = Array.from(computedStyle).reduce((str, property) => {
    return `${str}${property}:${computedStyle.getPropertyValue(property)};`;
  }, '');

  // 克隆元素并添加样式
  const clonedElement = element.cloneNode(true);
  clonedElement.style.cssText = styleString;

  return clonedElement.outerHTML;
}

/**
 * 简化的复制函数，直接使用
 * @param {HTMLElement|string} target - DOM元素或选择器
 * @param {boolean} showMessage - 是否显示消息
 */
export async function simpleCopy(target, showMessage = true) {
  try {
    let element;
    if (typeof target === 'string') {
      element = document.querySelector(target);
    } else {
      element = target;
    }

    await copyDomElement(element, { showMessage });
    return true;
  } catch (error) {
    if (showMessage) {
      console.error('复制失败:', error.message);
    }
    return false;
  }
}

// 默认导出
export default {
  copyDomElement,
  copyElementBySelector,
  copyMultipleElements,
  getElementWithStyles,
  simpleCopy
};
