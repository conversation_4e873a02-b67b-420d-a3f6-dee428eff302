<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOM清理功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-container {
            border: 2px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .header {
            background-color: #007bff;
            color: white;
            padding: 10px;
            margin: -20px -20px 20px -20px;
            border-radius: 6px 6px 0 0;
        }
        .title {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
        }
        .btn-group {
            margin: 20px 0;
            text-align: center;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .edit-button {
            background-color: #28a745;
            font-size: 12px;
            padding: 5px 10px;
        }
        .toolbar {
            background-color: #6c757d;
            color: white;
            padding: 5px;
            margin: 5px 0;
        }
        .no-copy {
            background-color: #dc3545;
            color: white;
            padding: 10px;
            margin: 10px 0;
        }
        .result-container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }
        .result-box {
            flex: 1;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            background-color: white;
        }
        .result-box h3 {
            margin-top: 0;
            color: #495057;
        }
        .html-output {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>DOM清理功能测试</h1>
    <p>测试清理DOM元素，移除不需要的内容（类似cleanHtmlContent的效果）</p>

    <div class="btn-group">
        <button onclick="testCleanDom()">测试DOM清理</button>
        <button onclick="showOriginal()">显示原始内容</button>
        <button onclick="showCleaned()">显示清理后内容</button>
        <button onclick="clearResults()">清除结果</button>
    </div>

    <!-- 测试内容区域 -->
    <div id="test-content" class="test-container">
        <div class="header" data-v-12345>
            <div class="title">测试内容区域</div>
            <div class="toolbar">这是工具栏（应该被移除）</div>
        </div>
        
        <div class="content" v-if="showContent">
            <h2>简报标题示例</h2>
            <p>这是一个包含<strong>粗体文字</strong>和<em>斜体文字</em>的段落。</p>
            
            <div class="no-copy">这个内容不应该被复制（应该被移除）</div>
            
            <p>这里有一个<a href="https://www.example.com" target="_blank">外部链接</a>的示例。</p>
            
            <button class="edit-button" onclick="alert('编辑')">编辑按钮（应该被移除）</button>
            
            <h3>数据表格示例</h3>
            <table>
                <thead>
                    <tr>
                        <th>品牌</th>
                        <th>市场份额</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>安踏</td>
                        <td>18.2%</td>
                        <td><button class="edit-button">编辑</button></td>
                    </tr>
                    <tr>
                        <td>耐克</td>
                        <td>16.8%</td>
                        <td><button class="edit-button">编辑</button></td>
                    </tr>
                </tbody>
            </table>

            <script>
                console.log('这个脚本应该被移除');
            </script>
            
            <style>
                .should-be-removed { color: red; }
            </style>
            
            <div contenteditable="true" draggable="true" onclick="alert('click')">
                这个div有很多属性，清理后应该只保留内容
            </div>
        </div>
    </div>

    <div class="result-container">
        <div class="result-box">
            <h3>原始HTML</h3>
            <div id="original-html" class="html-output"></div>
        </div>
        <div class="result-box">
            <h3>清理后HTML</h3>
            <div id="cleaned-html" class="html-output"></div>
        </div>
    </div>

    <script>
        // 清理配置
        const cleanConfig = {
            elementsToRemove: [
                '[data-v-*]',
                '[v-*]',
                '.no-copy',
                '.hidden-in-copy',
                '.edit-button',
                '.toolbar',
                '.controls',
                '.btn-group',
                'script',
                'style',
                'noscript'
            ],
            
            attributesToRemove: [
                'data-v-*',
                'v-*',
                'contenteditable',
                'draggable',
                'onclick',
                'onload',
                'onerror'
            ],
            
            elementsToReplace: {},
            elementsToHide: []
        };

        // 清理DOM元素
        function cleanDomForCopy(element, customConfig = {}) {
            const config = { ...cleanConfig, ...customConfig };
            const clonedElement = element.cloneNode(true);
            
            // 1. 移除指定的元素
            config.elementsToRemove.forEach(selector => {
                try {
                    const elements = clonedElement.querySelectorAll(selector);
                    elements.forEach(el => el.remove());
                } catch (e) {
                    console.warn('无效选择器:', selector);
                }
            });
            
            // 2. 替换指定的元素
            Object.entries(config.elementsToReplace).forEach(([selector, replacement]) => {
                try {
                    const elements = clonedElement.querySelectorAll(selector);
                    elements.forEach(el => {
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = replacement;
                        el.parentNode.replaceChild(tempDiv.firstChild, el);
                    });
                } catch (e) {
                    console.warn('元素替换失败:', selector);
                }
            });
            
            // 3. 隐藏指定的元素
            config.elementsToHide.forEach(selector => {
                try {
                    const elements = clonedElement.querySelectorAll(selector);
                    elements.forEach(el => {
                        el.style.display = 'none';
                    });
                } catch (e) {
                    console.warn('元素隐藏失败:', selector);
                }
            });
            
            // 4. 移除指定的属性
            const allElements = clonedElement.querySelectorAll('*');
            allElements.forEach(el => {
                Array.from(el.attributes).forEach(attr => {
                    config.attributesToRemove.forEach(attrPattern => {
                        if (attrPattern.includes('*')) {
                            const pattern = attrPattern.replace('*', '.*');
                            const regex = new RegExp(pattern);
                            if (regex.test(attr.name)) {
                                el.removeAttribute(attr.name);
                            }
                        } else if (attr.name === attrPattern) {
                            el.removeAttribute(attr.name);
                        }
                    });
                });
                
                // 移除空的class属性
                if (el.className === '') {
                    el.removeAttribute('class');
                }
            });
            
            return clonedElement;
        }

        // 测试函数
        function testCleanDom() {
            const element = document.getElementById('test-content');
            const originalHtml = element.innerHTML;
            const cleanedElement = cleanDomForCopy(element);
            const cleanedHtml = cleanedElement.innerHTML;
            
            document.getElementById('original-html').textContent = originalHtml;
            document.getElementById('cleaned-html').textContent = cleanedHtml;
            
            console.log('原始HTML长度:', originalHtml.length);
            console.log('清理后HTML长度:', cleanedHtml.length);
            console.log('清理后的contextHtml参数:', {
                contextHtml: cleanedHtml,
                timestamp: new Date().toISOString(),
                source: 'test-function'
            });
        }

        function showOriginal() {
            const element = document.getElementById('test-content');
            document.getElementById('original-html').textContent = element.innerHTML;
            document.getElementById('cleaned-html').textContent = '';
        }

        function showCleaned() {
            const element = document.getElementById('test-content');
            const cleanedElement = cleanDomForCopy(element);
            document.getElementById('cleaned-html').textContent = cleanedElement.innerHTML;
            document.getElementById('original-html').textContent = '';
        }

        function clearResults() {
            document.getElementById('original-html').textContent = '';
            document.getElementById('cleaned-html').textContent = '';
        }

        // 页面加载完成后自动测试
        window.onload = function() {
            console.log('DOM清理功能测试页面加载完成');
            testCleanDom();
        };
    </script>
</body>
</html>
