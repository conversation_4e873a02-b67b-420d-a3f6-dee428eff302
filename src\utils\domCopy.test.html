<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOM复制功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-container {
            border: 2px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .header {
            background-color: #007bff;
            color: white;
            padding: 10px;
            margin: -20px -20px 20px -20px;
            border-radius: 6px 6px 0 0;
        }
        .title {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .btn-group {
            margin: 20px 0;
            text-align: center;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background-color: #e9ecef;
        }
        a {
            color: #007bff;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        .highlight {
            background-color: yellow;
            padding: 2px 4px;
        }
        strong {
            color: #495057;
        }
        em {
            color: #6f42c1;
            font-style: italic;
        }
    </style>
</head>
<body>
    <h1>DOM复制功能测试</h1>
    <p>这个页面用于测试模拟浏览器手动选中复制的功能效果。</p>

    <div class="btn-group">
        <button onclick="testCopy()">测试复制功能</button>
        <button onclick="testSelectionCopy()">测试选择复制</button>
        <button onclick="testClipboardAPI()">测试剪贴板API</button>
        <button onclick="clearResults()">清除结果</button>
    </div>

    <div id="results"></div>

    <!-- 测试内容区域 -->
    <div id="test-content" class="test-container">
        <div class="header">
            <div class="title">测试内容区域</div>
        </div>
        
        <div class="content">
            <h2>简报标题示例</h2>
            <p>这是一个包含<strong>粗体文字</strong>和<em>斜体文字</em>的段落。</p>
            <p>这里有一个<a href="https://www.example.com" target="_blank">外部链接</a>的示例。</p>
            
            <div class="highlight">这是一个高亮显示的div内容</div>
            
            <h3>数据表格示例</h3>
            <table>
                <thead>
                    <tr>
                        <th>品牌</th>
                        <th>市场份额</th>
                        <th>增长率</th>
                        <th>备注</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>安踏</td>
                        <td>18.2%</td>
                        <td>+3.5%</td>
                        <td>篮球/儿童/户外</td>
                    </tr>
                    <tr>
                        <td>耐克</td>
                        <td>16.8%</td>
                        <td>-1.2%</td>
                        <td>跑步/时尚联名</td>
                    </tr>
                    <tr>
                        <td>李宁</td>
                        <td>12.5%</td>
                        <td>+2.1%</td>
                        <td>国潮设计/羽毛球</td>
                    </tr>
                </tbody>
            </table>

            <p>表格数据来源：<a href="#" onclick="return false;">市场研究报告2024</a></p>
            
            <div>
                <h4>要点总结</h4>
                <ul>
                    <li>本土品牌市场份额持续增长</li>
                    <li>国际品牌面临挑战</li>
                    <li>消费者偏好向国潮转移</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 模拟复制工具函数
        async function copyDomElement(element, options = {}) {
            if (!element) {
                throw new Error('没有可复制的元素');
            }

            const config = {
                includeStyles: true,
                preserveFormatting: true,
                fallbackToText: true,
                showMessage: true,
                ...options
            };

            // 方法1: 使用Selection API
            try {
                const success = await copyUsingSelection(element);
                if (success) {
                    return { method: 'Selection API', success: true };
                }
            } catch (error) {
                console.warn('Selection API 复制失败:', error);
            }

            // 方法2: 使用现代 Clipboard API
            try {
                const success = await copyUsingClipboardAPI(element, config);
                if (success) {
                    return { method: 'Clipboard API', success: true };
                }
            } catch (error) {
                console.warn('Clipboard API 复制失败:', error);
            }

            // 方法3: 降级到纯文本复制
            if (config.fallbackToText) {
                try {
                    const textContent = element.innerText || element.textContent || '';
                    await navigator.clipboard.writeText(textContent);
                    return { method: 'Text Fallback', success: true };
                } catch (error) {
                    console.warn('纯文本复制失败:', error);
                }
            }

            throw new Error('所有复制方法都失败了');
        }

        function copyUsingSelection(element) {
            return new Promise((resolve, reject) => {
                try {
                    const range = document.createRange();
                    const selection = window.getSelection();
                    
                    // 保存当前选择状态
                    const originalRanges = [];
                    for (let i = 0; i < selection.rangeCount; i++) {
                        originalRanges.push(selection.getRangeAt(i));
                    }
                    
                    selection.removeAllRanges();
                    range.selectNodeContents(element);
                    selection.addRange(range);
                    
                    const successful = document.execCommand('copy');
                    
                    // 恢复原始选择状态
                    selection.removeAllRanges();
                    originalRanges.forEach(range => selection.addRange(range));
                    
                    if (successful) {
                        resolve(true);
                    } else {
                        reject(new Error('execCommand copy 失败'));
                    }
                } catch (error) {
                    reject(error);
                }
            });
        }

        async function copyUsingClipboardAPI(element, config) {
            if (!navigator.clipboard || !navigator.clipboard.write) {
                throw new Error('Clipboard API 不支持');
            }

            const htmlContent = element.innerHTML;
            const textContent = element.innerText || element.textContent || '';
            
            const clipboardItems = {};
            
            if (config.preserveFormatting && htmlContent) {
                clipboardItems['text/html'] = new Blob([htmlContent], { type: 'text/html' });
            }
            
            if (textContent) {
                clipboardItems['text/plain'] = new Blob([textContent], { type: 'text/plain' });
            }
            
            const clipboardItem = new ClipboardItem(clipboardItems);
            await navigator.clipboard.write([clipboardItem]);
            
            return true;
        }

        // 测试函数
        async function testCopy() {
            const element = document.getElementById('test-content');
            const resultsDiv = document.getElementById('results');
            
            try {
                const result = await copyDomElement(element);
                showResult(`✅ 复制成功！使用方法: ${result.method}`, 'success');
            } catch (error) {
                showResult(`❌ 复制失败: ${error.message}`, 'error');
            }
        }

        async function testSelectionCopy() {
            const element = document.getElementById('test-content');
            
            try {
                const success = await copyUsingSelection(element);
                showResult('✅ Selection API 复制成功！', 'success');
            } catch (error) {
                showResult(`❌ Selection API 复制失败: ${error.message}`, 'error');
            }
        }

        async function testClipboardAPI() {
            const element = document.getElementById('test-content');
            
            try {
                const success = await copyUsingClipboardAPI(element, { preserveFormatting: true });
                showResult('✅ Clipboard API 复制成功！', 'success');
            } catch (error) {
                showResult(`❌ Clipboard API 复制失败: ${error.message}`, 'error');
            }
        }

        function showResult(message, type) {
            const resultsDiv = document.getElementById('results');
            const resultElement = document.createElement('div');
            resultElement.className = `result ${type}`;
            resultElement.innerHTML = `${new Date().toLocaleTimeString()}: ${message}`;
            resultsDiv.appendChild(resultElement);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // 页面加载完成后的提示
        window.onload = function() {
            showResult('页面加载完成，可以开始测试复制功能', 'success');
            showResult('提示：复制后可以粘贴到Word、记事本等应用中查看效果', 'success');
        };
    </script>
</body>
</html>
