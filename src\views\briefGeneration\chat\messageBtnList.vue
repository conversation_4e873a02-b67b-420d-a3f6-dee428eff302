<template>
  <div :class="['btn-list', msg.type]">
    <el-tooltip
      v-for="btn of btnList"
      :key="btn.name"
      :content="btn.tip"
      effect="dark"
      placement="top"
      popper-class="tooltip-adjust"
    >
      <div :class="['btn', btn.name]" @click="btn.handler()">
        <inline-svg :src="btn.icon" :style="btn.style" />
      </div>
    </el-tooltip>
  </div>
</template>

<script>
import InlineSvg from 'vue-inline-svg'
import copy from 'copy-to-clipboard'

export default {
  components: {
    InlineSvg
  },
  props: {
    msg: {
      type: Object,
      required: true
    },
    /**
     * 获取按钮列表的函数
     */
    fnBtnList: {
      type: Function,
      required: false
    },
  },
  data () {
    return {
      copied: false,
      btnMap: {
        'copy': {
          name: 'copy',
          icon: '/icons/copy.svg',
          tip: '复制',
          style: {},
          handler: () => {
            this.copyRenderedContent()
            this.copied = true
            setTimeout(() => {
              this.copied = false
            }, 600)
          }
        },
        'copied': {
          name: 'copied',
          icon: '/icons/check-mark.svg',
          tip: '已复制',
          style: {},
          handler: () => { }
        },
        'thumbs-up': {
          name: 'thumbs-up',
          icon: '/icons/thumbs-up.svg',
          tip: '点赞',
          style: {},
          handler: () => {
            this.$emit('like')
          }
        },
        'thumbs-down': {
          name: 'thumbs-down',
          icon: '/icons/thumbs-up.svg',
          tip: '反对',
          style: {
            transform: 'rotate(180deg)'
          },
          handler: () => {
            this.$emit('dislike')
          }
        },
        'thumbs-up-filled': {
          name: 'thumbs-up-filled',
          icon: '/icons/thumbs-up-filled.svg',
          tip: '取消点赞',
          style: {
            fill: '#ff6600'
          },
          handler: () => {
            this.$emit('revert-like')
          }
        },
        'thumbs-down-filled': {
          name: 'thumbs-down-filled',
          icon: '/icons/thumbs-up-filled.svg',
          tip: '取消反对',
          style: {
            transform: 'rotate(180deg)',
            fill: '#ff6600'
          },
          handler: () => {
            this.$emit('cancel-dislike')
          }
        },
        edit: {
          name: 'edit',
          icon: '/icons/edit.svg',
          tip: '编辑',
          style: {},
          handler: () => {
            this.$emit('edit')
          }
        },
        insert: {
          name: 'insert',
          icon: '/icons/edit.svg',
          tip: '插入',
          handler: () => {
            this.$emit('insert')
          }
        },
        regenerate: {
          name: 'regenerate',
          icon: '/icons/reload.svg',
          tip: '重新生成',
          style: {},
          handler: () => {
            this.$emit('regenerate')
          }
        }

      }
    }
  },
  methods: {
    // 复制渲染后的内容
    copyRenderedContent() {
      try {
        // 查找父组件中的 message 元素
        const messageElement = this.findMessageElement()
        if (!messageElement) {
          // 降级到复制原始文本
          copy(this.msg.content)
          return
        }

        // 获取渲染后的 HTML 内容
        const renderedHTML = this.getCleanHTML(messageElement)
        // 使用 copy-to-clipboard 库复制 HTML 格式内容
        const success = copy(renderedHTML, {
          format: 'text/html',
          debug: false
        })
        if (!success) {
          // 如果 HTML 复制失败，降级到纯文本
          copy(this.msg.content)
        }
      } catch (error) {
        console.warn('复制失败，使用降级方案:', error)
        copy(this.msg.content)
      }
    },

    // 查找包含渲染内容的 message 元素
    findMessageElement() {
      // 从当前组件开始向上查找
      let parent = this.$parent
      while (parent) {
        if (parent.$refs && parent.$refs.message) {
          return parent.$refs.message
        }
        parent = parent.$parent
      }
      return null
    },

    // 获取清理后的 HTML 内容
    getCleanHTML(element) {
      // 克隆元素以避免修改原始DOM
      const clonedElement = element.cloneNode(true)

      // 移除不需要的元素（如按钮、控制元素等）
      const unwantedSelectors = [
        '.btn-list',
        '.message-btn-list',
        '.scroll-mark',
        'button',
        '.loading'
      ]

      unwantedSelectors.forEach(selector => {
        const elements = clonedElement.querySelectorAll(selector)
        elements.forEach(el => el.remove())
      })
      return clonedElement.innerHTML
    },
  },
  computed: {
    justify () {
      if (this.msg.type === 'question') {
        return 'flex-end'
      } else {
        return 'flex-start'
      }
    },
    btnList () {
      // 通过回调，确定展示的按钮列表
      if (this.fnBtnList) {
        const btns = this.fnBtnList(this.msg)
        for (let idx=0; idx<btns.length; idx++) {
          if (btns[idx] === 'copy') {
            btns[idx] = this.copied ? 'copied' : 'copy'
          }
        }

        return btns.map((item) =>{ 
          if (typeof item === 'string') {
            return this.btnMap[item]
          } else if (typeof item === 'object' && item.name && this.btnMap[item.name]) {
            return Object.assign({}, this.btnMap[item.name], item)
          } else if (typeof item === 'object') {
            return item
          }
        })
      }

      // 没有回调，则只在回复下展示
      const btns = []

      if (this.msg.type === 'answer') {
        btns.push(this.copied ? 'copied' : 'copy')
      }

      return btns.map((name) => this.btnMap[name])
    }
  }
};
</script>

<style scoped>
.btn-list {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  margin-top: 12px;
}
.btn-list.answer {
  justify-content: flex-start;
  margin-top: 0;
}
.btn-list.answer .btn {
  margin-right: 12px;
  margin-left: 0;
}
.btn {
  display: block;
  width: 26px;
  height: 26px;
  border: 0;
  cursor: pointer;
  border-radius: 12px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-left: 10px;
  position: relative;
}
.btn:hover svg {
  color: #6AF6FF;
}
.btn svg {
  width: 16px;
  height: 16px;
  color: #6AF6FF;
  pointer-events: none;
}
</style>

<style>
.tooltip-adjust .popper__arrow {
  bottom: -5px !important;
}
</style>
界定行业
安踏品牌所在的精准行业定义99
安踏是中国领先的运动鞋服行业品牌，核心业务涵盖专业运动装备、运动休闲服饰及儿童运动产品。根据其产品结构和市场定位，安踏属于运动服饰与装备制造业，主要竞争对手包括耐克、阿迪达斯、李宁等[Link]。

广义行业定义

运动消费品行业：包括运动鞋服、运动器材、健身设备及相关配件，覆盖专业竞技、大众健身、户外休闲等场景。
主要产品类别：运动鞋（跑步鞋、篮球鞋等）、运动服装（训练服、比赛服等）、运动配件（背包、护具等）、户外装备（登山、滑雪等）[Link]。
狭义行业定义

运动鞋服行业：专注于设计、生产和销售运动相关的功能性服装与鞋类产品，强调科技性能（如缓震、透气）与时尚设计结合。
主要细分品类：
专业运动系列：篮球鞋、跑步鞋、训练服等；
运动休闲系列：时尚运动服饰、联名款；
儿童运动线：儿童专业运动鞋服；
户外运动系列：登山、滑雪等专业装备（通过收购品牌如始祖鸟、萨洛蒙扩展）[Link]。
数据来源：[中华网财经]、[财经头条]、[腾讯新闻]

发展历史
中国运动鞋服行业发展历史

时期	关键词	文化/事件
20世纪50-70年代	起步阶段	计划经济时期，运动鞋服以国营工厂生产为主，产品单一，主要为专业运动员提供装备，如回力鞋的诞生（1956年）[【知识库】]。
20世纪80年代	代工兴起	改革开放后，国际品牌（如耐克、阿迪达斯）进入中国，福建晋江等地出现代工厂，本土企业开始学习技术与管理模式[【知识库】]。
1990-2000年	品牌萌芽	安踏（1991年）、李宁（1990年）等本土品牌成立，从代工转向自主品牌，主打性价比，赞助体育赛事提升知名度[【知识库】]。
2001-2008年	奥运红利	北京申奥成功（2001年）推动全民运动热潮，本土品牌加速扩张，安踏签约CBA（2004年），李宁成为奥运赞助商[【知识库】]。
2009-2015年	行业调整	库存危机（2012年）导致行业洗牌，安踏通过零售转型（DTC模式）率先复苏，国际品牌抢占高端市场[【知识库】]。
2016-2020年	国潮崛起	李宁“中国李宁”系列（2018年）引爆国潮，安踏收购始祖鸟（2019年）布局高端户外，本土品牌设计力提升[【知识库】]。
2021年至今	科技竞争	碳板跑鞋（如安踏C202）、环保材料（再生纤维）成为技术焦点，线上直播与社群营销重塑消费场景[【知识库】]。
西方运动鞋服行业发展历史