<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTTP环境复制功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-container {
            border: 2px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .header {
            background-color: #007bff;
            color: white;
            padding: 10px;
            margin: -20px -20px 20px -20px;
            border-radius: 6px 6px 0 0;
        }
        .btn-group {
            margin: 20px 0;
            text-align: center;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.https {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.http {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .json-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>HTTP环境复制功能测试</h1>
    
    <!-- 环境检测 -->
    <div id="environment-status"></div>
    
    <div class="btn-group">
        <button onclick="testHttpCopy()">测试HTTP兼容复制</button>
        <button onclick="testClipboardAPI()">测试Clipboard API</button>
        <button onclick="generateJsonOnly()">仅生成JSON</button>
        <button onclick="clearOutput()">清除输出</button>
    </div>

    <!-- 测试内容区域 -->
    <div id="test-content" class="test-container">
        <div class="header">
            <div class="title">测试内容区域</div>
        </div>
        
        <div class="content">
            <h2>简报标题示例</h2>
            <p>这是一个包含<strong>粗体文字</strong>和<em>斜体文字</em>的段落。</p>
            <p>这里有一个<a href="https://www.example.com" target="_blank">外部链接</a>的示例。</p>
            
            <h3>数据表格示例</h3>
            <table>
                <thead>
                    <tr>
                        <th>品牌</th>
                        <th>市场份额</th>
                        <th>增长率</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>安踏</td>
                        <td>18.2%</td>
                        <td>+3.5%</td>
                    </tr>
                    <tr>
                        <td>耐克</td>
                        <td>16.8%</td>
                        <td>-1.2%</td>
                    </tr>
                </tbody>
            </table>

            <div>
                <h4>要点总结</h4>
                <ul>
                    <li>本土品牌市场份额持续增长</li>
                    <li>国际品牌面临挑战</li>
                    <li>消费者偏好向国潮转移</li>
                </ul>
            </div>
        </div>
    </div>

    <div id="json-output" class="json-output" style="display: none;"></div>

    <script>
        // 存储复制内容的JSON变量
        let copiedContentJson = '';

        // 检测环境是否支持现代Clipboard API
        const isClipboardAPISupported = () => {
            return (
                navigator.clipboard && 
                navigator.clipboard.write && 
                window.isSecureContext // HTTPS或localhost环境
            );
        };

        // 生成复制内容的JSON字符串
        const generateCopyContentJson = (element) => {
            if (!element) return '';
            
            const copyData = {
                timestamp: new Date().toISOString(),
                source: 'brief-generation',
                environment: {
                    protocol: window.location.protocol,
                    isSecureContext: window.isSecureContext,
                    clipboardAPISupported: isClipboardAPISupported()
                },
                content: {
                    html: element.innerHTML,
                    text: element.innerText || element.textContent || '',
                    elementCount: element.querySelectorAll('*').length,
                    hasImages: element.querySelectorAll('img').length > 0,
                    hasTables: element.querySelectorAll('table').length > 0,
                    hasLinks: element.querySelectorAll('a').length > 0
                },
                format: {
                    preserveFormatting: true,
                    includeStyles: true,
                    cleanedForCopy: true
                }
            };
            
            return JSON.stringify(copyData, null, 2);
        };

        // HTTP环境兼容的复制方法
        const copyDomAsSelection = (element) => {
            try {
                // 1. 生成JSON字符串存储
                copiedContentJson = generateCopyContentJson(element);
                console.log('复制内容JSON:', copiedContentJson);

                // 2. 创建选择范围进行复制
                const range = document.createRange();
                const selection = window.getSelection();

                // 清除现有选择
                selection.removeAllRanges();

                // 选择整个DOM元素内容
                range.selectNodeContents(element);

                // 添加到选择中
                selection.addRange(range);

                // 执行复制命令（HTTP环境下仍然可用）
                const successful = document.execCommand('copy');

                if (successful) {
                    showMessage('✅ 内容已复制到剪贴板（保持原始格式）', 'success');
                    handleCopySuccess(copiedContentJson);
                    return true;
                } else {
                    throw new Error('复制命令执行失败');
                }

                // 清除选择
                selection.removeAllRanges();

            } catch (error) {
                console.error('复制失败:', error);
                showMessage('❌ 复制失败：' + error.message, 'error');
                return false;
            }
        };

        // HTTP环境兼容的现代API复制方法
        const copyDomAsClipboard = async (element) => {
            try {
                // 1. 生成JSON字符串存储
                copiedContentJson = generateCopyContentJson(element);
                console.log('复制内容JSON:', copiedContentJson);

                // 2. 获取内容
                const htmlContent = element.innerHTML;
                const textContent = element.innerText || element.textContent;

                // 3. 检查环境支持情况
                if (isClipboardAPISupported()) {
                    // HTTPS环境：使用现代Clipboard API
                    try {
                        const clipboardItem = new ClipboardItem({
                            'text/html': new Blob([htmlContent], { type: 'text/html' }),
                            'text/plain': new Blob([textContent], { type: 'text/plain' })
                        });

                        await navigator.clipboard.write([clipboardItem]);
                        showMessage('✅ 内容已复制到剪贴板（包含格式）', 'success');
                        handleCopySuccess(copiedContentJson);
                        return true;
                    } catch (clipboardError) {
                        console.warn('Clipboard API失败，降级到文本复制:', clipboardError);
                        await navigator.clipboard.writeText(textContent);
                        showMessage('✅ 内容已复制到剪贴板（纯文本）', 'success');
                        handleCopySuccess(copiedContentJson);
                        return true;
                    }
                } else {
                    // HTTP环境：降级到execCommand
                    console.warn('当前环境不支持Clipboard API，使用execCommand方法');
                    return copyDomAsSelection(element);
                }
            } catch (error) {
                console.error('复制失败:', error);
                showMessage('❌ 复制失败：' + error.message, 'error');
                return false;
            }
        };

        // 处理复制成功的回调
        const handleCopySuccess = (jsonData) => {
            console.log('复制成功，JSON数据已生成:', jsonData);
            
            // 显示JSON数据
            const outputDiv = document.getElementById('json-output');
            outputDiv.style.display = 'block';
            outputDiv.textContent = jsonData;
            
            // 解析JSON数据显示元数据
            try {
                const copyData = JSON.parse(jsonData);
                showMessage(`📊 复制元数据：元素${copyData.content.elementCount}个，图片${copyData.content.hasImages ? '有' : '无'}，表格${copyData.content.hasTables ? '有' : '无'}，链接${copyData.content.hasLinks ? '有' : '无'}`, 'info');
            } catch (e) {
                console.error('JSON解析失败:', e);
            }
        };

        // 测试函数
        function testHttpCopy() {
            const element = document.getElementById('test-content');
            copyDomAsSelection(element);
        }

        function testClipboardAPI() {
            const element = document.getElementById('test-content');
            copyDomAsClipboard(element);
        }

        function generateJsonOnly() {
            const element = document.getElementById('test-content');
            copiedContentJson = generateCopyContentJson(element);
            
            const outputDiv = document.getElementById('json-output');
            outputDiv.style.display = 'block';
            outputDiv.textContent = copiedContentJson;
            
            showMessage('✅ JSON数据已生成（未执行复制操作）', 'info');
        }

        function clearOutput() {
            document.getElementById('json-output').style.display = 'none';
            document.getElementById('json-output').textContent = '';
            copiedContentJson = '';
        }

        function showMessage(message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.textContent = message;
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 10px 15px;
                border-radius: 4px;
                color: white;
                font-weight: bold;
                z-index: 1000;
                max-width: 300px;
                background-color: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
            `;
            
            document.body.appendChild(messageDiv);
            
            setTimeout(() => {
                document.body.removeChild(messageDiv);
            }, 3000);
        }

        // 页面加载时检测环境
        window.onload = function() {
            const statusDiv = document.getElementById('environment-status');
            const isSecure = window.isSecureContext;
            const protocol = window.location.protocol;
            const clipboardSupported = isClipboardAPISupported();
            
            statusDiv.innerHTML = `
                <div class="status ${isSecure ? 'https' : 'http'}">
                    <strong>当前环境：</strong>${protocol} ${isSecure ? '(安全环境)' : '(非安全环境)'}<br>
                    <strong>Clipboard API支持：</strong>${clipboardSupported ? '✅ 支持' : '❌ 不支持'}<br>
                    <strong>推荐方案：</strong>${clipboardSupported ? 'Clipboard API' : 'execCommand (兼容模式)'}
                </div>
            `;
            
            console.log('环境检测完成:', {
                protocol,
                isSecureContext: isSecure,
                clipboardAPISupported: clipboardSupported
            });
        };
    </script>
</body>
</html>
