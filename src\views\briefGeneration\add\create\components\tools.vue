<template>
  <div class="tools" v-if="pageType === 'add' || pageType === 'edit'">
    <div class="left">
      <el-icon @click="handleJump" v-show="pageType === 'add'"
        ><ArrowLeft
      /></el-icon>
      <div v-if="!isEdit" class="name">{{ configData.title }}</div>
      <div v-else class="editInput">
        <el-input ref="titleInputRef" v-model="configData.title" :maxlength="25" :show-word-limit="true"
          @blur="useInput" />
      </div>
      <div @click="handleEdit" class="editIcon">
        <i class="ri-pencil-fill"></i>
      </div>
    </div>
    <!-- <stepBox /> -->
    <div class="right">
      <div
        class="btnBg"
        title="提交"
        @click="submitbtn"
        :style="{ cursor: startGenerating ? 'not-allowed' : 'pointer' }"
      >
        <img src="@/assets/images/brief/submit.svg" class="icon" />
      </div>
      <div
        class="btnBg"
        title="保存"
        @click="
          () => {
            savebtn('click');
          }
        "
        :style="{ cursor: startGenerating ? 'not-allowed' : 'pointer' }"
      >
        <img src="@/assets/images/brief/save.svg" class="icon" />
      </div>
    </div>
  </div>
  <div class="tools" v-else-if="pageType === 'view'">
    <div class="left">
      <div class="name">{{ configData.title }}</div>
    </div>
    <div class="right">
      <div class="btnBg" title="下载" @click="downClisk" v-show="isFinish">
        <img src="@/assets/images/brief/download.svg" class="icon" />
      </div>
      <div class="btnBg" title="复制内容（保持格式）" @click="exportAsText" v-show="isFinish">
        <i class="ri-file-copy-line"></i>
      </div>
      <div class="btnBg" title="复制为富文本" @click="copyDomAsClipboard" v-show="isFinish">
        <i class="ri-clipboard-line"></i>
      </div>
    </div>
  </div>
</template>
<script setup>
import {
  ref,
  reactive,
  defineProps,
  onMounted,
  getCurrentInstance,
  onUnmounted,
} from "vue";
import { useRouter, useRoute, onBeforeRouteLeave } from "vue-router";
import html2canvas from 'html2canvas';
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
// import stepBox from "@/views/briefGeneration/add/config/step.vue";
import {
  // form,
  activeStep,
  pageType,
  pageId,
  paragraphs,
  // saveParagraphs,
  startGenerating,
  referId,
  menuList,
  resetForm,
  configData,
  scrollRef
} from "@/views/briefGeneration/add/create/js/sharedState.js";
import { refreshSideBar } from "@/views/briefGeneration/add/create/js/paragraphUtils.js";
import { reportSave, reportDetail } from "@/api/briefGeneration/index.js";
import { html2Text, htmlToWordFormat } from "@/utils/index.js";
const props = defineProps({
  isFinish: {
    type: Boolean,
    required: true,
    default: false,
  },
});
let timer = null;
const router = useRouter();
const route = useRoute();
const isEdit = ref(false);
const isSubmitSuccess = ref(false); //用于设置提交成功直接跳转，不用提示保存
const titleInputRef = ref(null);
const isReturn = ref(false); //用于设置返回按钮，不用清空configData
let loadingInstance;
const handleJump = () => {
  isReturn.value = true;
  router.push("/briefGeneration/add/config");
};
const handleEdit = () => {
  isEdit.value = true;
  nextTick(() => {
    // 延迟获取焦点，避免与其他元素冲突
    setTimeout(() => {
      if (titleInputRef.value && titleInputRef.value.focus) {
        titleInputRef.value.focus();
      }
    }, 100);
  });
};
const useInput = () => {
  if (configData.value.title.trim() === "") {
    ElMessage({
      type: "error",
      message: "简报名称不能为空!",
    });
    return;
  }
  isEdit.value = false;
};
onMounted(() => {
  if (route.params.type === "add") {
    activeStep.value = 2; // 设置当前步骤为简报生成
  } else if (route.params.type === "edit") {
    activeStep.value = 3; // 设置当前步骤为简报优化
  }
  if (route.params.type === "add" || route.params.type === "edit") {
    timer = null;
    timer = setInterval(() => {
      if (!startGenerating.value && paragraphs.value.length > 0) {
        savebtn("auto");
      }
    }, 5 * 60 * 1000);
  }
});
const { proxy } = getCurrentInstance();
// 下载
const downClisk = () => {
  if (!pageId.value) return;

  ElMessageBox.confirm(
    `确认导出【${configData.value.title}】简报吗？`,
    "确认下载?",
    {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning",
      customClass: "junzhiMessage",
    }
  )
    .then(() => {
      proxy.getDownload(
        `/report/download?id=${pageId.value}`,
        `${configData.value.title}.docx`,
        {},
        'junzhiLoading'
      );
    })
    .catch(() => {});
};
// 清理HTML内容的函数
const cleanHtmlContent = async (htmlString) => {
  if (!htmlString) return '';

  // 创建一个临时div来操作HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlString;

  // 移除action-buttons相关的div
  const actionButtons = tempDiv.querySelectorAll('.action-buttons, [class*="action-button"]');
  actionButtons.forEach(el => el.remove());

  // 移除iconChat2相关的元素
  const iconChat2Elements = tempDiv.querySelectorAll('.iconChat2, [class*="iconChat2"]');
  iconChat2Elements.forEach(el => el.remove());

  // 移除think-con相关的元素
  const thinkConElements = tempDiv.querySelectorAll('.think-con, [class*="think-con"]');
  thinkConElements.forEach(el => el.remove());
  // 移除公式隐藏的元素
  const katexElements = tempDiv.querySelectorAll('.katex-mathml, [class*="katex-mathml"]');
  katexElements.forEach(el => el.remove());
  // 移除所有display:none的元素（v-show隐藏的元素）
  const hiddenElements = tempDiv.querySelectorAll('[style*="display: none"], [style*="display:none"]');
  hiddenElements.forEach(el => el.remove());

  // 也可以通过计算样式来检查隐藏元素（更准确但性能较低）
  // const allElements = tempDiv.querySelectorAll('*');
  // allElements.forEach(el => {
  //   const computedStyle = window.getComputedStyle(el);
  //   if (computedStyle.display === 'none') {
  //     el.remove();
  //   }
  // });
  // 处理iframe
  await processIframes(tempDiv)
  // 获取清理后的HTML
  let cleanedHtml = tempDiv.innerHTML;

  // 移除所有HTML注释（包括Vue条件渲染注释和普通HTML注释）
  cleanedHtml = cleanedHtml.replace(/<!--[\s\S]*?-->/g, '');

  // 移除Vue的scoped CSS属性 (data-v-xxxxxxxx="")
  cleanedHtml = cleanedHtml.replace(/\s*data-v-[a-f0-9]{8}="[^"]*"/g, '');

  // 移除其他可能的Vue相关属性
  cleanedHtml = cleanedHtml.replace(/\s*data-v-[a-f0-9]+="[^"]*"/g, '');
  return cleanedHtml.trim();
};

// 将HTML内容转换为Word格式的函数
// div、p标签提取文本保留换行，a、img、table标签保持HTML格式，特别处理header类
const convertHtmlToWordFormat = (htmlContent, options = {}) => {
  if (!htmlContent) return '';

  // 先清理HTML内容
  const cleanedHtml = cleanHtmlContent(htmlContent);
// console.log('cleanedHtml :>> ', JSON.stringify(cleanedHtml));
  // 使用专门的Word格式转换函数
  const wordFormattedText = htmlToWordFormat(cleanedHtml, {
    preserveLineBreaks: true,     // 保持换行
    removeExtraSpaces: true,      // 移除多余空格
    keepHtmlTags: ['a', 'img', 'table','strong', 'b', 'em', 'i', 'u','blockquote', 'ul','ol'], // 保持HTML格式的标签
    textOnlyTags: ['div', 'p', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',], // 只提取文本的标签
    addLineBreakTags: ['div', 'p', 'br', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',], // 需要添加换行的标签
    ...options
  });
  return wordFormattedText;
};

// 兼容原有函数名
const convertHtmlToText = convertHtmlToWordFormat;

// 存储复制内容的JSON变量
let copiedContentJson = ref('');

// 检测环境是否支持现代Clipboard API
const isClipboardAPISupported = () => {
  return (
    navigator.clipboard &&
    navigator.clipboard.write &&
    window.isSecureContext // HTTPS或localhost环境
  );
};

// 生成复制内容的JSON字符串
const generateCopyContentJson = (element) => {
  if (!element) return '';

  const copyData = {
    timestamp: new Date().toISOString(),
    source: 'brief-generation',
    content: {
      html: element.innerHTML,
      text: element.innerText || element.textContent || '',
      // 可以添加更多元数据
      elementCount: element.querySelectorAll('*').length,
      hasImages: element.querySelectorAll('img').length > 0,
      hasTables: element.querySelectorAll('table').length > 0,
      hasLinks: element.querySelectorAll('a').length > 0
    },
    format: {
      preserveFormatting: true,
      includeStyles: true,
      cleanedForCopy: true
    }
  };

  return JSON.stringify(copyData, null, 2);
};

// HTTP环境兼容的复制方法
const copyDomAsSelection = () => {
  if (!scrollRef.value) {
    ElMessage.error('没有可复制的内容');
    return;
  }

  try {
    // 1. 生成JSON字符串存储
    copiedContentJson.value = generateCopyContentJson(scrollRef.value);
    console.log('复制内容JSON:', copiedContentJson.value);

    // 2. 创建选择范围进行复制
    const range = document.createRange();
    const selection = window.getSelection();

    // 清除现有选择
    selection.removeAllRanges();

    // 选择整个DOM元素内容
    range.selectNodeContents(scrollRef.value);

    // 添加到选择中
    selection.addRange(range);

    // 执行复制命令（HTTP环境下仍然可用）
    const successful = document.execCommand('copy');

    if (successful) {
      ElMessage.success('内容已复制到剪贴板（保持原始格式）');

      // 3. 可以在这里调用API传递JSON数据
      handleCopySuccess(copiedContentJson.value);
    } else {
      throw new Error('复制命令执行失败');
    }

    // 清除选择
    selection.removeAllRanges();

  } catch (error) {
    console.error('复制失败:', error);
    ElMessage.error('复制失败，请手动选择复制');
  }
};

// 处理复制成功的回调
const handleCopySuccess = (jsonData) => {
  console.log('复制成功，JSON数据已生成:', jsonData);

  // 这里可以调用API或其他处理逻辑
  // 例如：发送到服务器、存储到本地等

  // 示例：解析JSON数据
  try {
    const copyData = JSON.parse(jsonData);
    console.log('复制的内容元数据:', {
      时间戳: copyData.timestamp,
      元素数量: copyData.content.elementCount,
      包含图片: copyData.content.hasImages,
      包含表格: copyData.content.hasTables,
      包含链接: copyData.content.hasLinks
    });
  } catch (e) {
    console.error('JSON解析失败:', e);
  }
};

// HTTP环境兼容的现代API复制方法
const copyDomAsClipboard = async () => {
  if (!scrollRef.value) {
    ElMessage.error('没有可复制的内容');
    return;
  }

  try {
    // 1. 生成JSON字符串存储
    copiedContentJson.value = generateCopyContentJson(scrollRef.value);
    console.log('复制内容JSON:', copiedContentJson.value);

    // 2. 获取内容
    const htmlContent = scrollRef.value.innerHTML;
    const textContent = scrollRef.value.innerText || scrollRef.value.textContent;

    // 3. 检查环境支持情况
    if (isClipboardAPISupported()) {
      // HTTPS环境：使用现代Clipboard API
      try {
        const clipboardItem = new ClipboardItem({
          'text/html': new Blob([htmlContent], { type: 'text/html' }),
          'text/plain': new Blob([textContent], { type: 'text/plain' })
        });

        await navigator.clipboard.write([clipboardItem]);
        ElMessage.success('内容已复制到剪贴板（包含格式）');
        handleCopySuccess(copiedContentJson.value);
      } catch (clipboardError) {
        console.warn('Clipboard API失败，降级到文本复制:', clipboardError);
        await navigator.clipboard.writeText(textContent);
        ElMessage.success('内容已复制到剪贴板（纯文本）');
        handleCopySuccess(copiedContentJson.value);
      }
    } else {
      // HTTP环境：降级到execCommand
      console.warn('当前环境不支持Clipboard API，使用execCommand方法');
      copyDomAsSelection();
    }
  } catch (error) {
    console.error('复制失败:', error);
    ElMessage.error('复制失败：' + error.message);
  }
};

// 获取当前复制内容的JSON字符串（供外部调用）
const getCopiedContentJson = () => {
  return copiedContentJson.value;
};

// 手动生成内容JSON（不执行复制操作）
const generateContentJson = () => {
  if (!scrollRef.value) {
    ElMessage.error('没有可生成的内容');
    return '';
  }

  const jsonData = generateCopyContentJson(scrollRef.value);
  copiedContentJson.value = jsonData;
  console.log('内容JSON已生成:', jsonData);
  return jsonData;
};

// 主要的导出函数，优先使用选择复制
const exportAsText = () => {
  // 优先使用模拟手动选择的方式（HTTP环境兼容）
  copyDomAsSelection();
};

// 暴露方法供父组件调用
defineExpose({
  getCopiedContentJson,
  generateContentJson,
  exportAsText,
  copyDomAsClipboard
});
// 替换iframe为图片
const processIframes = async (element) => {
  // 1. 获取所有iframe和所有transport目标
  const iframes = element.querySelectorAll("iframe[srcdoc]"); //复制的元素中的iframe
  const transportTargets = element.querySelectorAll('[id^="html-uniqId-"]'); //复制的元素中的iframe对应的目标元素
  // 2. 一一对应处理
  for (let i = 0; i < iframes.length; i++) {
    const iframe = iframes[i];
    //1.替换为图片，暂时先不用
    // const target = transportTargets[i];
    // if (!target) continue;

    // try {
    //   const srcdoc = iframe.getAttribute("srcdoc");
    //   if (srcdoc) {
    //     // 创建临时iframe渲染ECharts
    //     const tempIframe = document.createElement("iframe");
    //     tempIframe.style.width = iframe.offsetWidth ? iframe.offsetWidth + "px" : "900px";
    //     tempIframe.style.height = iframe.offsetHeight ? iframe.offsetHeight + "px" : "600px";
    //     tempIframe.style.border = "none";
    //     tempIframe.style.position = "absolute";
    //     tempIframe.style.left = "-9999px";
    //     tempIframe.srcdoc = srcdoc;
    //     document.body.appendChild(tempIframe);

    //     // 等待iframe加载完成
    //     await new Promise((resolve) => {
    //       tempIframe.onload = () => setTimeout(resolve, 1000);
    //       // setTimeout(resolve, 2000); // 最长等待2秒
    //     });
    //     try {
    //       const iframeDoc = tempIframe.contentDocument || tempIframe.contentWindow.document;
    //       const chartContainer = iframeDoc.body;
    //       let width = 900, height = 600;
    //       try {
    //         if (tempIframe.contentWindow && tempIframe.contentWindow.document && tempIframe.contentWindow.document.documentElement) {
    //           width = parseInt(tempIframe.contentWindow.document.documentElement.scrollWidth) || 900;
    //           height = parseInt(tempIframe.contentWindow.document.documentElement.scrollHeight) || 600;
    //         }
    //       } catch (e) {
    //         width = 900;
    //         height = 600;
    //       }
    //       if (chartContainer) {
    //         const canvas = await html2canvas(chartContainer, {
    //           scale: 1,
    //           useCORS: true,
    //           allowTaint: true,
    //           backgroundColor: "#ffffff",
    //           width: width,
    //           height: height,
    //         });

    //         const img = document.createElement("img");
    //         img.src = canvas.toDataURL("image/jpeg", 0.8);
    //         img.style.width = width;
    //         img.style.height = height;
    //         img.style.maxWidth = "100%";
    //         img.style.display = "block";
    //         img.style.margin = "10px 0";
    //         // 替换目标元素内容为图片
    //         target.innerHTML = "";
    //         target.appendChild(img);
    //       } else {
    //         throw new Error("找不到图表容器");
    //       }
    //     } catch (canvasError) {
    //       // 占位符逻辑
    //       target.innerHTML = `<div style="width:${width};height:${height};display:flex;align-items:center;justify-content:center;border:1px dashed #ccc;">图表内容</div>`;
    //     }

    //     document.body.removeChild(tempIframe);
    //   }
    //   // 删除原来的iframe标签
    //   if (iframe.parentNode) {
    //     iframe.parentNode.removeChild(iframe);
    //   }
    // } catch (error) {
    //   // 占位符逻辑
    //   if (target) {
    //     target.innerHTML = `<div style="width:900px;height:600px;display:flex;align-items:center;justify-content:center;border:1px dashed #ccc;">图表内容</div>`;
    //   }
    //   // 删除原来的iframe标签
    //   if (iframe.parentNode) {
    //     iframe.parentNode.removeChild(iframe);
    //   }
    // }
    //2.删除原来的iframe标签
    if (iframe.parentNode) {
      iframe.parentNode.removeChild(iframe);
    }
  }
}
const checkout = async (status, saveType) => {
  if (paragraphs.value.length === 0) {
    console.log("1 :>> ", 1);
    saveType !== "auto" && ElMessage.error("暂无简报");
    return;
  }
  if (startGenerating.value) {
    console.log("2 :>> ", 2);
    saveType !== "auto" && ElMessage.error("正在生成中，请稍后再试");
    return;
  }
  const hasEditing = paragraphs.value.some((v) => {
    return v?.some(
      (item) =>
        item.mode === "edit" ||
        (item.subSections &&
          item.subSections.some(
            (sub) =>
              sub.mode === "edit" ||
              (sub.subSections &&
                sub.subSections.some(
                  (thirdSub) =>
                    thirdSub.mode === "edit" ||
                    (thirdSub.subSections &&
                      thirdSub.subSections.some(
                        (fourthSub) => fourthSub.mode === "edit"
                      ))
                ))
          ))
    );
  });
  if (hasEditing) {
    console.log("3 :>> ", 3);
    saveType !== "auto" && ElMessage.error("当前有内容正在编辑，请先完成编辑");
    return;
  }
  refreshSideBar();
  let params = {
    title: configData.value.title,
    referId: referId.value || 0,
    catalog: JSON.stringify(menuList.value),
  };
  params.option = {
    brandName: configData.value.brandName,
    version: configData.value.version, // 简报版本
    dataBank: configData.value.dataBank,
    internet: configData.value.internet,
    labelConfigId: configData.value.labelConfigId,
  };
  if (pageId.value) {
    params.id = pageId.value;
  }
  params.status = status; // 0设置状态为保存,1设置状态为提交
  params.context = JSON.stringify(paragraphs.value);
  if (status == 1) {
    const rawHtml = scrollRef.value ? scrollRef.value.innerHTML : '';
    let cleanedHtml = await cleanHtmlContent(rawHtml);
    params.contextHtml = cleanedHtml;
  }
  return params;
};
// 保存逻辑
const savebtn = async (saveType) => {
  loadingInstance = ElLoading.service({ text: "正在保存数据，请稍候", background: "rgba(0, 0, 0, 0.7)", customClass: 'junzhiLoading' })
  let params = await checkout(0, saveType);
  console.log("params :>> ", params);
  if (!params) return;
  reportSave(params)
    .then((res) => {
      loadingInstance.close();
      console.log("保存成功", res);
      // saveParagraphs.value = JSON.parse(JSON.stringify(paragraphs.value));
      if (!pageId.value && res.data) pageId.value = res.data;
      ElMessage.success("保存成功！");
      // reportDetail().then((re) => {
      //   console.log("查询", re);
      //   let list = re.data;
      //   list.forEach((item) => {
      //     if (item.id == pageId.value) {
      //       console.log(item);
      //       contentData.value = item;
      //     }
      //   });
      // });
    })
    .catch((err) => {
      loadingInstance.close();
      // ElMessage.error('操作失败，请稍后重试！')
    });
  // }
};
// 提交逻辑
const submitbtn = () => {
  ElMessageBox.confirm(`是否确认提交简报？`, "是否提交？", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    customClass: "junzhiMessage",
  })
    .then(async () => {
      loadingInstance = ElLoading.service({ text: "正在提交数据，请稍候", background: "rgba(0, 0, 0, 0.7)", customClass: 'junzhiLoading' })
      let params = await checkout(1);
      console.log("params :>> ", params);
      if (!params) return;
      reportSave(params).then((res) => {
        loadingInstance.close();
        if (res.code == 200) {
          isSubmitSuccess.value = true; // 设置提交成功状态
          ElMessage.success({
            message: "提交成功",
            duration: 500,
          });
          setTimeout(() => {
            router.push("/briefGeneration/list");
          }, 500);
        }
      }).catch((err) => {
        loadingInstance.close();
        // ElMessage.error('操作失败，请稍后重试！')
      });;
    })
    .catch(() => {
    });
}
// 优化后的路由离开守卫 - 简化版本
const beforeRouteLeave = (_to, _from, next) => {
  // 辅助函数：安全离开（重置表单并跳转）
  const safeLeave = (type) => {
    resetForm(type);
    next();
  };

  // 辅助函数：显示保存确认对话框
  const showSaveDialog = () => {
    ElMessageBox.confirm(
      "存在未提交的内容, 需要为您保存最新的简报?",
      "是否保存?",
      {
        confirmButtonText: "保存",
        cancelButtonText: "取消",
        type: "warning",
        customClass: "junzhiMessage",
      }
    )
      .then(() => {
        savebtn();
        safeLeave();
      })
      .catch(safeLeave);
  };

  // 1. 查看模式或提交成功 - 直接离开
  if (pageType.value === "view" || isSubmitSuccess.value) {
    return safeLeave();
  }
  if (isReturn.value) {
    return safeLeave("return");
  }

  // 2. 正在生成中 - 显示确认对话框
  if (startGenerating.value) {
    ElMessageBox.confirm(
      "正在生成中，退出可能导致生成内容失败，请确认是否要退出？",
      "是否退出?",
      {
        confirmButtonText: "退出",
        cancelButtonText: "取消",
        type: "warning",
        customClass: "junzhiMessage",
      }
    )
      .then(() => {
        safeLeave();
      })
      .catch(safeLeave);
    return;
  }

  // 3. 没有内容且没有ID - 直接离开
  if (paragraphs.value.length === 0 && !pageId.value) {
    return safeLeave();
  }

  // 4. 有内容但未保存 - 询问是否保存
  if (!pageId.value && paragraphs.value.length > 0) {
    return showSaveDialog();
  }

  // 5. 其他情况 - 直接离开
  safeLeave();
};
onBeforeRouteLeave(beforeRouteLeave);
onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
});
</script>
<style lang='scss' scoped>
.tools {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  color: #6af6ff;
  line-height: 18px;
  .left {
    display: flex;
    align-items: center;
    .name {
      font-size: 18px;
      font-weight: 600;
    }
    .name,
    .editInput {
      margin: 0 7px;
    }
    .editIcon {
      width: 18px;
      height: 18px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #1c375b;
      background: #00b0ff;
      border-radius: 2px;
    }
  }
  .right {
    display: flex;
    align-items: center;
    padding-right: 20px;
    .btnBg {
      width: 25px;
      height: 25px;
      border-radius: 50px;
      background: linear-gradient(
        136.1deg,
        rgba(75, 175, 247, 1) -17.4%,
        rgba(9, 60, 98, 1) 100%
      );
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #ffffff;
      cursor: pointer;
      .icon {
        width: 14px;
        height: 14px;
      }
      &:hover {
        box-shadow: 1px 0px 2px 0px rgba(0, 229, 255, 1);
        border: 1px solid rgba(0, 229, 255, 1);
      }
    }
    .btnBg ~ .btnBg {
      margin-left: 24px;
    }
  }
}
</style>