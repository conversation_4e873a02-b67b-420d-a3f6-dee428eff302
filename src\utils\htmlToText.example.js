/**
 * HTML转文本功能使用示例
 * 
 * 这个文件展示了如何使用 htmlToFormattedText 函数将HTML内容转换为格式化的纯文本
 */

import { html2Text, htmlToFormattedText } from '@/utils/index.js';

// 示例HTML内容
const sampleHtml = `
<div>
  <h1>简报标题</h1>
  <h2>第一章节</h2>
  <p>这是一个包含<strong>粗体文字</strong>和<em>斜体文字</em>的段落。</p>
  
  <h3>列表示例</h3>
  <ul>
    <li>无序列表项目1</li>
    <li>无序列表项目2</li>
    <li>无序列表项目3</li>
  </ul>
  
  <ol>
    <li>有序列表项目1</li>
    <li>有序列表项目2</li>
    <li>有序列表项目3</li>
  </ol>
  
  <h3>链接和代码</h3>
  <p>访问 <a href="https://www.example.com">示例网站</a> 了解更多信息。</p>
  <p>使用 <code>console.log()</code> 来输出调试信息。</p>
  
  <pre><code>
function example() {
  console.log("这是代码块");
}
  </code></pre>
  
  <h3>表格示例</h3>
  <table>
    <thead>
      <tr>
        <th>姓名</th>
        <th>年龄</th>
        <th>城市</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>张三</td>
        <td>25</td>
        <td>北京</td>
      </tr>
      <tr>
        <td>李四</td>
        <td>30</td>
        <td>上海</td>
      </tr>
    </tbody>
  </table>
  
  <hr>
  
  <blockquote>
    <p>这是一个引用块的内容。</p>
  </blockquote>
</div>
`;

// 使用示例

// 1. 简单HTML转文本（基础版本）
console.log('=== 基础HTML转文本 ===');
const basicText = html2Text(sampleHtml);
console.log(basicText);

// 2. 高级HTML转文本（保持格式）
console.log('\n=== 高级HTML转文本（默认配置） ===');
const formattedText = htmlToFormattedText(sampleHtml);
console.log(formattedText);

// 3. 自定义配置的HTML转文本
console.log('\n=== 自定义配置的HTML转文本 ===');
const customFormattedText = htmlToFormattedText(sampleHtml, {
  preserveLineBreaks: true,
  preserveSpacing: true,
  addListMarkers: true,
  addTableBorders: true,
  removeExtraSpaces: true,
  preserveLinks: true,
  linkFormat: '【text】(url)',  // 自定义链接格式
  maxLineLength: 50            // 限制行长度
});
console.log(customFormattedText);

// 4. 不保留换行的版本（适合单行显示）
console.log('\n=== 单行文本版本 ===');
const singleLineText = htmlToFormattedText(sampleHtml, {
  preserveLineBreaks: false,
  removeExtraSpaces: true,
  addListMarkers: false,
  preserveLinks: false
});
console.log(singleLineText);

// 5. 在Vue组件中的使用示例
export const useHtmlToText = () => {
  /**
   * 将HTML内容转换为可复制的文本
   * @param {string} htmlContent - HTML内容
   * @param {Object} options - 配置选项
   * @returns {string} 格式化的文本
   */
  const convertToText = (htmlContent, options = {}) => {
    return htmlToFormattedText(htmlContent, {
      preserveLineBreaks: true,
      preserveSpacing: true,
      addListMarkers: true,
      removeExtraSpaces: true,
      preserveLinks: true,
      linkFormat: '[text](url)',
      ...options
    });
  };

  /**
   * 复制HTML内容为文本到剪贴板
   * @param {string} htmlContent - HTML内容
   * @param {Object} options - 配置选项
   */
  const copyHtmlAsText = async (htmlContent, options = {}) => {
    const textContent = convertToText(htmlContent, options);
    
    try {
      await navigator.clipboard.writeText(textContent);
      return { success: true, text: textContent };
    } catch (error) {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = textContent;
      textArea.style.position = 'fixed';
      textArea.style.left = '-9999px';
      document.body.appendChild(textArea);
      textArea.select();
      
      try {
        document.execCommand('copy');
        document.body.removeChild(textArea);
        return { success: true, text: textContent };
      } catch (fallbackError) {
        document.body.removeChild(textArea);
        return { success: false, error: fallbackError, text: textContent };
      }
    }
  };

  /**
   * 获取元素的HTML并转换为文本
   * @param {HTMLElement} element - DOM元素
   * @param {Object} options - 配置选项
   * @returns {string} 格式化的文本
   */
  const getElementTextContent = (element, options = {}) => {
    if (!element) return '';
    const htmlContent = element.innerHTML;
    return convertToText(htmlContent, options);
  };

  return {
    convertToText,
    copyHtmlAsText,
    getElementTextContent
  };
};

// 6. 在简报生成中的具体使用示例
export const useBriefTextExport = () => {
  const { convertToText, copyHtmlAsText } = useHtmlToText();

  /**
   * 导出简报内容为文本
   * @param {HTMLElement} scrollElement - 滚动容器元素
   * @returns {Promise<Object>} 导出结果
   */
  const exportBriefAsText = async (scrollElement) => {
    if (!scrollElement) {
      return { success: false, error: '没有可导出的内容' };
    }

    const htmlContent = scrollElement.innerHTML;
    
    // 简报专用的配置
    const briefOptions = {
      preserveLineBreaks: true,
      preserveSpacing: true,
      addListMarkers: true,
      removeExtraSpaces: true,
      preserveLinks: true,
      linkFormat: '[text](url)',
      maxLineLength: 0  // 不限制行长度
    };

    try {
      const result = await copyHtmlAsText(htmlContent, briefOptions);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  return {
    exportBriefAsText
  };
};

// 导出所有功能
export {
  html2Text,
  htmlToFormattedText,
  useHtmlToText,
  useBriefTextExport
};
