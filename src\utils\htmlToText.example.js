/**
 * HTML转文本功能使用示例
 * 
 * 这个文件展示了如何使用 htmlToFormattedText 函数将HTML内容转换为格式化的纯文本
 */

import { html2Text, htmlToFormattedText } from '@/utils/index.js';

// 示例HTML内容 - 针对新需求设计
const sampleHtml = `
<div class="content">
  <h1>简报标题</h1>
  <div class="section">
    <p>这是第一个段落，包含普通文本。</p>
    <p>这是第二个段落，包含<a href="https://www.example.com">链接文字</a>。</p>
  </div>

  <div class="image-section">
    <p>下面是一张图片：</p>
    <img src="https://example.com/image.jpg" alt="示例图片" width="300" height="200">
  </div>

  <div class="table-section">
    <p>数据表格：</p>
    <table border="1" cellpadding="5" cellspacing="0">
      <thead>
        <tr>
          <th>姓名</th>
          <th>年龄</th>
          <th>城市</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>张三</td>
          <td>25</td>
          <td>北京</td>
        </tr>
        <tr>
          <td>李四</td>
          <td>30</td>
          <td>上海</td>
        </tr>
      </tbody>
    </table>
  </div>

  <div class="mixed-content">
    <h2>混合内容示例</h2>
    <p>这个段落包含<a href="https://github.com">GitHub链接</a>和普通文字。</p>
    <div>这是一个div中的文本内容</div>
    <span>这是span中的内容</span>
    <br>
    <p>换行后的段落</p>
  </div>
</div>
`;

// 使用示例

// 1. 简单HTML转文本（基础版本）
console.log('=== 基础HTML转文本 ===');
const basicText = html2Text(sampleHtml);
console.log(basicText);

// 2. 新的选择性HTML转文本（默认配置）
console.log('\n=== 选择性HTML转文本（默认配置） ===');
const selectiveText = htmlToFormattedText(sampleHtml);
console.log(selectiveText);

// 预期输出：
// 简报标题
// 这是第一个段落，包含普通文本。
// 这是第二个段落，包含<a href="https://www.example.com">链接文字</a>。
// 下面是一张图片：
// <img src="https://example.com/image.jpg" alt="示例图片" width="300" height="200">
// 数据表格：
// <table border="1" cellpadding="5" cellspacing="0">...</table>
// 混合内容示例
// 这个段落包含<a href="https://github.com">GitHub链接</a>和普通文字。
// 这是一个div中的文本内容
// 这是span中的内容
// 换行后的段落

// 3. 自定义配置 - 更多标签保持HTML格式
console.log('\n=== 自定义配置 - 保持更多HTML标签 ===');
const customText = htmlToFormattedText(sampleHtml, {
  keepHtmlTags: ['a', 'img', 'table', 'thead', 'tbody', 'tr', 'td', 'th', 'strong', 'em'],
  textOnlyTags: ['div', 'p', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
  addLineBreakTags: ['div', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
  preserveLineBreaks: true,
  removeExtraSpaces: true
});
console.log(customText);

// 4. 只保留特定HTML标签
console.log('\n=== 只保留链接和图片HTML格式 ===');
const linksAndImagesOnly = htmlToFormattedText(sampleHtml, {
  keepHtmlTags: ['a', 'img'],
  textOnlyTags: ['div', 'p', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'table', 'thead', 'tbody', 'tr', 'td', 'th'],
  addLineBreakTags: ['div', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
  preserveLineBreaks: true,
  removeExtraSpaces: true
});
console.log(linksAndImagesOnly);

// 5. 在Vue组件中的使用示例
export const useHtmlToText = () => {
  /**
   * 将HTML内容转换为可复制的文本
   * @param {string} htmlContent - HTML内容
   * @param {Object} options - 配置选项
   * @returns {string} 格式化的文本
   */
  const convertToText = (htmlContent, options = {}) => {
    return htmlToFormattedText(htmlContent, {
      preserveLineBreaks: true,
      preserveSpacing: true,
      addListMarkers: true,
      removeExtraSpaces: true,
      preserveLinks: true,
      linkFormat: '[text](url)',
      ...options
    });
  };

  /**
   * 复制HTML内容为文本到剪贴板
   * @param {string} htmlContent - HTML内容
   * @param {Object} options - 配置选项
   */
  const copyHtmlAsText = async (htmlContent, options = {}) => {
    const textContent = convertToText(htmlContent, options);
    
    try {
      await navigator.clipboard.writeText(textContent);
      return { success: true, text: textContent };
    } catch (error) {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = textContent;
      textArea.style.position = 'fixed';
      textArea.style.left = '-9999px';
      document.body.appendChild(textArea);
      textArea.select();
      
      try {
        document.execCommand('copy');
        document.body.removeChild(textArea);
        return { success: true, text: textContent };
      } catch (fallbackError) {
        document.body.removeChild(textArea);
        return { success: false, error: fallbackError, text: textContent };
      }
    }
  };

  /**
   * 获取元素的HTML并转换为文本
   * @param {HTMLElement} element - DOM元素
   * @param {Object} options - 配置选项
   * @returns {string} 格式化的文本
   */
  const getElementTextContent = (element, options = {}) => {
    if (!element) return '';
    const htmlContent = element.innerHTML;
    return convertToText(htmlContent, options);
  };

  return {
    convertToText,
    copyHtmlAsText,
    getElementTextContent
  };
};

// 6. 在简报生成中的具体使用示例
export const useBriefTextExport = () => {
  const { convertToText, copyHtmlAsText } = useHtmlToText();

  /**
   * 导出简报内容为文本
   * @param {HTMLElement} scrollElement - 滚动容器元素
   * @returns {Promise<Object>} 导出结果
   */
  const exportBriefAsText = async (scrollElement) => {
    if (!scrollElement) {
      return { success: false, error: '没有可导出的内容' };
    }

    const htmlContent = scrollElement.innerHTML;
    
    // 简报专用的配置
    const briefOptions = {
      preserveLineBreaks: true,
      preserveSpacing: true,
      addListMarkers: true,
      removeExtraSpaces: true,
      preserveLinks: true,
      linkFormat: '[text](url)',
      maxLineLength: 0  // 不限制行长度
    };

    try {
      const result = await copyHtmlAsText(htmlContent, briefOptions);
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  return {
    exportBriefAsText
  };
};

// 导出所有功能
export {
  html2Text,
  htmlToFormattedText,
  useHtmlToText,
  useBriefTextExport
};
