<template>
    <div class="container">
        <div class="content" :style="{'height':props.isTitle?'calc(100vh - 80px)':'calc(100vh)','padding':props.isDebugging?'10px 10%':'10px 20%'}">
            <div class="ai-chat-content" :style="{'height':props.isTitle && !isSupportFile && !isSupportImage ?'calc( 100vh - 270px)':
            props.isTitle && (isSupportFile || isSupportImage) ? 'calc( 100vh - 250px)':'calc( 100vh - 200px)'}" ref="recordsBox">
                <div :style="{'margin-top':prefaceHeight && prefaceState && 
                ((isKeyList && currentInstruct && currentInstruct.keyList && currentInstruct.keyList.length>0 ) 
                || (isOpenQuestionShow && currentInstruct && currentInstruct.suggested_questions))?
                prefaceHeight+10+'px':'0',
                    'height':prefaceHeight && prefaceState&& 
                ((isKeyList && currentInstruct && currentInstruct.keyList && currentInstruct.keyList.length>0 ) 
                || (isOpenQuestionShow && currentInstruct && currentInstruct.suggested_questions))?
                'calc( 100vh - '+(prefaceHeight+280)+'px'+' )':'',
                    'overflow-y':prefaceHeight && prefaceState&& 
                ((isKeyList && currentInstruct && currentInstruct.keyList && currentInstruct.keyList.length>0 ) 
                || (isOpenQuestionShow && currentInstruct && currentInstruct.suggested_questions))?
                'auto':''
                }">
                    <!--用户输入-->
                    <div class="user-input" v-if="isKeyList && currentInstruct && currentInstruct.user_input_form && currentInstruct.user_input_form.length>0 
                    && currentInstruct.user_input_form[0].keyCode!='' && currentInstruct.user_input_form[0].keyName!=''">
                        <div style="display: flex;justify-content: left;margin-bottom: 20px;font-weight: 600;">用户输入</div>
                        <div v-for="(item,index) in currentInstruct.user_input_form" >
                            <div v-if="Object.keys(item)[0]" class="key-input">
                                <div style="width: 25%;">{{ item[Object.keys(item)[0]].label }}</div>
                                <div style="width: 70%;" v-if="Object.keys(item)[0] && Object.keys(item)[0] == 'text-input'">
                                    <el-input v-model="keyList[item[Object.keys(item)[0]].variable]" :placeholder="item[Object.keys(item)[0]].label" type="text"
                                    :maxlength="item[Object.keys(item)[0]].max_length" />
                                </div>
                                <div style="width: 70%;" v-if="Object.keys(item)[0] && Object.keys(item)[0]=='paragraph'">
                                    <el-input v-model="keyList[item[Object.keys(item)[0]].variable]" type="textarea" show-word-limit rows="4" :placeholder="item[Object.keys(item)[0]].label"
                                    :maxlength="item[Object.keys(item)[0]].max_length" />
                                </div>
                                <div style="width: 70%;" v-if="Object.keys(item)[0] && Object.keys(item)[0]=='select'">
                                    <el-select clearable v-model="keyList[item[Object.keys(item)[0]].variable]" :placeholder="item[Object.keys(item)[0]].label" style="width: 100%;">
                                        <el-option v-for="(item,index) in item[Object.keys(item)[0]].options" 
                                        :key="item" :label="item" :value="item" />
                                    </el-select>
                                </div>
                                <div style="width: 70%;" v-if="Object.keys(item)[0] && Object.keys(item)[0] == 'number'">
                                    <el-input v-model="keyList[item[Object.keys(item)[0]].variable]" :placeholder="item[Object.keys(item)[0]].label" type="text" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--开场预置问题-->
                    <div class="open-question-box" v-if="isOpenQuestionShow && currentInstruct">
                        <!--开场白-->
                        <div style="display: flex;justify-content: left;" v-if="currentInstruct.opening_statement && !prefaceState">
                            <div class="prologue">{{ currentInstruct.opening_statement }}</div>
                        </div>
                        
                        <div v-if="currentInstruct.suggested_questions && currentInstruct.suggested_questions.length>0">
                            <div v-for="(item , index) in currentInstruct.suggested_questions.slice(0,3)" style="display: flex;margin-bottom: 5px;">
                                <div class="open-question"  @click="openQuestionClick(item)" v-if="item">
                                    {{ item }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-for="(item, index) in props.messageItemManager.refMessageItemList" :class="item.isUser?'user-record-box':'robot-record-box'" 
                :key="index">
                    <div class="head-img" :style="{'background-color':!item.isUser?'#dfdfff':''}">
                        <img :src="currentInstruct.applicationIcon?baseUrl+currentInstruct.applicationIcon:TeamDefaultIcon" v-if="!item.isUser">
                    </div>
                    <div :class="item.isUser?userContentClassName:robotContentClassName" >
                        <div class="user-name">
                            {{ item.isUser ? '我' : item.roleInfo.name?item.roleInfo.name : currentInstruct.applicationName}}
                        </div>
                        <!--状态信息-->
                        
                        <!--内容-->
                        <div :class="item.isUser?userBoxClassName:robotBoxClassName" v-if="(item.isUser && item.content) || !item.isUser">
                            <div v-if="item.isUser" style="display: flex;align-items: center;">
                                <img :src="pdfRed" style="height: 25px;width: 20px;margin-right: 5px;"
                                v-if="item.content && item.content.includes('.pdf')">
                                {{ item.content }}
                            </div>
                            <template v-else>
                                <!--工作流-->
                                <div class="workflow-info-box" v-if="item.chat_message &&  item.chat_message.workflow" 
                                :style="{'width':item.chat_message.workflow.isOpen?'100%':''}">
                                    <div class="workflow-info" >
                                        <div class="title" @click="()=>{
                                            item.chat_message.workflow.isOpen = !item.chat_message.workflow.isOpen
                                            isMoveScroll = false
                                        }">
                                            <img :src="greenYes" class="img"/>
                                            <div style="margin-left: 5px;">工作流</div>
                                            <el-icon style="margin-left: 5px;" v-if="!item.chat_message.workflow.isOpen"><ArrowDownBold /></el-icon>
                                            <el-icon style="margin-left: 5px;" v-if="item.chat_message.workflow.isOpen"><ArrowUpBold /></el-icon>
                                        </div>
                                        <div style="padding:5px" v-if="item.chat_message.workflow.isOpen" >
                                            <div v-for="(node,index) in item.chat_message.workflow.workflowList" class="node-box">
                                                <div class="node">
                                                    <div style="display: flex;align-items: center;">
                                                        <workflow-icon :icon="node.node_type" v-if="node.type!='tool'" :size="14" style="margin-right: 5px;" />
                                                        <div v-else style="width: 24px;height: 24px;background-color: white;border-radius: 0.5rem;"></div>
                                                        {{ node.title }}
                                                    </div>
                                                    <el-icon v-if="!node.status"><Loading /></el-icon>
                                                    <el-icon v-else-if="node.status=='succeeded'"><CircleCheck style="color: #3BC384;"/></el-icon>
                                                    <el-icon v-else><CircleClose style="color: red;"/></el-icon>
                                                </div>
                                            </div>
                                        </div>
                                        
                                    </div>
                                </div>
                                <message-renderer :cnt="item.content"/>
                            </template>
                            <!--操作-->
                            <div class="hidden-area" v-if="!item.isUser">
                                <div class="operation">
                                    <!--复制-->
                                    <div class="box">
                                        <el-tooltip content="复制" effect="light" placement="top" popper-class="tooltip-pop">
                                            <el-button :icon="CopyDocument" link @click="copyClick(item.content)"></el-button>
                                        </el-tooltip>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--引用出处-->
                        <div v-if="!item.isUser && item.retrieverResources && item.retrieverResources.length>0" class="knowledge-list">
                            <citation-list :data="item.retrieverResources"></citation-list>
                        </div>
                        <!--选项-->
                        <div v-if="!item.isUser && item.chat_message && item.chat_message.choice && item.chat_message.choice.length>0 && !item.choicesShow">
                            <div v-for="(choice,index) in item.chat_message.choice" class="choices"  @click="{item.choicesShow=true;choicesClick(choice)}">
                                {{ choice }}</div>
                        </div>
                        <!--建议问题-->
                        <div v-if="!item.isUser && suggested_questions" style="margin-top: 5px;">
                            <div v-for="(autosuggestion,index) in suggested_questions" class="choices" @click="autosuggestionClick(autosuggestion)" >{{ autosuggestion }}</div>
                        </div>
                        <!--用户传入图片或文件-->
                        <div v-if="item.isUser && item.annex" class="upload-file-show">
                            <!--文件-->
                            <div class="file file-justify-content" v-if="item.annex.uploadFileList && item.annex.uploadFileList.length>0">
                                <div v-for="(uploadFile,index) in item.annex.uploadFileList" class="file-show file-show-background">
                                    <div class="whole">
                                        <div class="left">
                                            <img :src="(uploadFile.name.split('.')[1]=='doc' || uploadFile.name.split('.')[1]=='docx'
                                            || uploadFile.name.split('.')[1]=='wps')?chatWord:uploadFile.name.split('.')[1]=='pdf'?chatPdf
                                            :uploadFile.name.split('.')[1]=='ppt'?chatPpt:uploadFile.name.split('.')[1]=='txt'?chatTxt:
                                            (uploadFile.name.split('.')[1]=='xls' || uploadFile.name.split('.')[1]=='xlsx'
                                            || uploadFile.name.split('.')[1]=='csv')?chatExcel:''" />
                                        </div>
                                        <div class="right">
                                            <div class="title">{{ uploadFile.name.split('.')[0] }}</div>
                                            <div style="text-align: left;line-height: 30px;">{{uploadFile.name.split('.')[1]}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--图片-->
                            <div class="image image-justify-content" v-if="item.annex.uploadImageList && item.annex.uploadImageList.length>0">
                                <div v-for="(uploadImage,index) in item.annex.uploadImageList" class="image-show">
                                    <el-image class="img"
                                    :src="uploadImage"
                                    :zoom-rate="1.2"
                                    :max-scale="7"
                                    :min-scale="0.2"
                                    :preview-src-list="[uploadImage]"
                                    :initial-index="4"
                                    fit="cover"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="head-img" v-if="item.isUser">
                        <!--baseUrl+-->
                        <img :src="userStore.avatar">
                    </div>
                </div>
                <div class="robot-record-box" v-if="props.messageItemManager.loading.value">
                    <div class="head-img" v-loading="!props.messageItemManager.waitting.value?false:true"
                        :style="{'background-color':props.messageItemManager.waitting.value?'#dfdfff':''}" >
                        <img :src="currentInstruct.applicationIcon?baseUrl+currentInstruct.applicationIcon:TeamDefaultIcon" v-if="!props.messageItemManager.waitting.value">
                    </div>
                    <div class="robot-content">
                        <div class="user-name" v-if="props.messageItemManager.waitting.value">
                            {{ currentInstruct.applicationName}}
                        </div>
                        <div class="user-name" v-if="!props.messageItemManager.waitting.value">
                            {{ props.messageItemManager.nowMessageRole.value.name || currentInstruct.applicationName}}
                        </div>
                        <div class="robot-record-item">
                            <!--工作流-->
                            <div class="workflow-info-box" v-if="props.messageItemManager.nowMessageChatMessage.value 
                            && props.messageItemManager.nowMessageChatMessage.value.workflow" 
                            :style="{'width':props.messageItemManager.nowMessageChatMessage.value.workflow.isOpen?'100%':''}">
                                <div class="workflow-info" >
                                    <div class="title" @click="()=>{
                                        props.messageItemManager.nowMessageChatMessage.value.workflow.isOpen = !props.messageItemManager.nowMessageChatMessage.value.workflow.isOpen
                                        isMoveScroll = false
                                    }">
                                        <el-icon><Loading /></el-icon>
                                        <div style="margin-left: 5px;">工作流</div>
                                        <el-icon style="margin-left: 5px;" v-if="!props.messageItemManager.nowMessageChatMessage.value.workflow.isOpen"><ArrowDownBold /></el-icon>
                                        <el-icon style="margin-left: 5px;" v-if="props.messageItemManager.nowMessageChatMessage.value.workflow.isOpen"><ArrowUpBold /></el-icon>
                                    </div>
                                    <div style="padding:5px" v-if="props.messageItemManager.nowMessageChatMessage.value.workflow.isOpen" >
                                        <div v-for="(node,index) in props.messageItemManager.nowMessageChatMessage.value.workflow.workflowList" class="node-box">
                                            <div class="node">
                                                <div style="display: flex;align-items: center;">
                                                    <workflow-icon :icon="node.node_type" v-if="node.type!='tool'" :size="14" style="margin-right: 5px;" />
                                                    <div v-else style="width: 24px;height: 24px;background-color: white;border-radius: 0.5rem;"></div>
                                                    {{ node.title }}
                                                </div>
                                                <el-icon v-if="!node.status"><Loading /></el-icon>
                                                <el-icon v-else-if="node.status=='succeeded'"><CircleCheck style="color: #3BC384;"/></el-icon>
                                                <el-icon v-else><CircleClose style="color: red;"/></el-icon>
                                            </div>
                                        </div>
                                    </div>
                                    
                                </div>
                            </div>
                            <div v-if="props.messageItemManager.waitting.value">
                                wait...
                            </div>
                            <template v-else>
                                <message-renderer :cnt="props.messageItemManager.nowMessageHtml.value" />
                            </template>
                        </div>
                         <!--停止响应-->
                         <div class="stop-responding" v-if="!props.messageItemManager.waitting.value" @click="stopRespondingTeam()">
                            <el-icon style="margin-right: 5px;"><VideoPause /></el-icon>
                            停止响应
                        </div>
                    </div>
                </div>
            </div>
            <div class="bottom-area">
                <div class="bar" v-if="!prefaceState">
                    <div class="tools-list" >
                        <!-- <div class="tools-item" @click="">
                            <img class="tool-icon" :src="picture" width="20px" height="20px"/>
                        </div> -->
                        <!--<div class="tools-item" @click="">
                            <img class="tool-icon" :src="exportsvg" width="20px" height="20px"/>
                        </div> -->
                        <el-tooltip :disabled="disabled" content="新会话" placement="top" effect="light">
                            <div class="tools-item" @click="openNewSession" v-if="!prefaceState">
                                <img class="tool-icon" :src="newSession" width="20px" height="20px"/>
                            </div>
                        </el-tooltip>
                    </div>
                </div>
                <div class="submit-area">
                    <div class="content-area">
                        <div class="user-input-area">
                            <div v-if="isSupportFile || isSupportImage" class="upload-file-handle">
                                <team-file-upload v-model="uploadFileList" :limit="1" v-if="isSupportFile"></team-file-upload>
                                <chat-upload-image v-model="uploadImageList" :limit="supportImageLimit?supportImageLimit:8" v-if="isSupportImage"></chat-upload-image>
                            </div>
                            <div class="top-area">
                                <div class="text-area">
                                    <textarea ref="userInputTextarea" v-model="userInputTextareaValue" :placeholder="userInputTextareaPlaceholer" autocomplete="off" 
                                        class="user-input" :class="{'show-tool-tip':userInputIsShowToolTip}" :style="{'text-indent': textAreaIndent}" 
                                        @keydown="handleKeyDown" @keydown.enter.shift.prevent="handleShiftEnter" @input="handleInput"
                                        ></textarea>
                                    <span ref="toolTip" class="tool-tip" 
                                        :style="{
                                            'display':userInputIsShowToolTip?'block':'none'
                                            ,'transform':toolTipTranslateY}"
                                    >{{ toolTipContent }}</span>
                                </div>
                            </div>
                            <div class="bottom-info-area">
                                <el-button type="primary" :icon="Promotion" size="small" round 
                                :disabled="isDisabledSend" 
                                @click="runTest"></el-button>
                                <!-- <div class="send-btn" :class="{'disabled':isDisabledSend}" @click="runTest"></div> -->
                            </div>
                            <div class="upload-file-show upload-file-show-send" v-if="(isSupportFile || isSupportImage) && ((uploadImageList && uploadImageList.length>0)
                            || (uploadFileList && uploadFileList.length>0))" style="margin-top: 10px;border-top: 1px solid #F1F0F5;">
                                <!--文件-->
                                <div class="file file-justify-content-send" v-if="uploadFileList && uploadFileList.length>0">
                                    <div v-for="(item,index) in uploadFileList" class="file-show file-show-background-send">
                                        <div class="whole">
                                            <div class="left">
                                                <img :src="(item.name.split('.')[1]=='doc' || item.name.split('.')[1]=='docx'
                                                || item.name.split('.')[1]=='wps')?chatWord:item.name.split('.')[1]=='pdf'?chatPdf
                                                :item.name.split('.')[1]=='ppt'?chatPpt:item.name.split('.')[1]=='txt'?chatTxt:
                                                (item.name.split('.')[1]=='xls' || item.name.split('.')[1]=='xlsx'
                                                || item.name.split('.')[1]=='csv')?chatExcel:''" />
                                            </div>
                                            <div class="right">
                                                <div class="title">{{ item.name.split('.')[0] }}</div>
                                                <div style="text-align: left;line-height: 30px;">{{item.name.split('.')[1]}}</div>
                                            </div>
                                        </div>
                                        <div class="del">
                                            <el-icon @click="delFile(item)"><CircleCloseFilled style="background-color: white;
                                            border-radius: 10px;"/></el-icon>
                                        </div>
                                    </div>
                                </div>
                                <!--图片-->
                                <div class="image image-justify-content-send" v-if="uploadImageList">
                                    <div v-for="(item,index) in uploadImageList" class="image-show">
                                        <el-image class="img"
                                        :src="item.url"
                                        :zoom-rate="1.2"
                                        :max-scale="7"
                                        :min-scale="0.2"
                                        :preview-src-list="[item.url]"
                                        :initial-index="4"
                                        fit="cover"
                                        />
                                        <div class="del">
                                            <el-icon @click="delImage(item)"><CircleCloseFilled style="background-color: white;
                                            border-radius: 10px;"/></el-icon>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="tip">内容由AI生成，无法确保真实准确。</div> -->
            </div>
        </div>
    </div>
</template>
<script setup>
    import { defineProps, nextTick,onBeforeUnmount,ref, watch } from 'vue';
    import {v4 as uuidv4} from 'uuid'
    import useUserStore from '@/store/modules/user'
    import { CircleCheck ,ArrowDownBold ,Fold ,ArrowUpBold,Promotion,Refresh,VideoPause,CopyDocument,CircleClose} from '@element-plus/icons-vue'
    import { getToken } from "@/utils/auth";
    import { TaskQueue } from './utils/taskQueue'
    import TeamDefaultIcon from '@/assets/icons/svg/team-default.svg'
    import MessageRenderer from '@/components/Chat/messageRenderer.vue'
    import newSession from "@/assets/icons/svg/newSession.svg" 
    import CitationList from '@/components/Chat/citationList.vue'
    import { conversationGenerate } from "@/api/YiMaiChat/sessionRecords"
    import { ElMessage } from 'element-plus'
    import { parse } from '@vue/compiler-sfc';
    import { getDetail } from '@/api/agent/body'
    import useClipboard from 'vue-clipboard3';
    import { fetchEventSource } from '@microsoft/fetch-event-source';
    import { closeSourceSession,getSuggestedQuestions,conversationRename,conversationHistory,
        stopResponding,saveUserMessage,saveUserMessageFile
    } from '@/api/YiMaiChat/sessionRecordsDify'
    import greenYes from '@/assets/icons/svg/green-yes.svg'
    import WorkflowIcon from '@/components/WorkflowIcon/index'
    import chatUploadImage from '@/components/chatUploadImage'
    import teamFileUpload from '@/components/teamFileUpload'
    import chatWord from '@/assets/icons/svg/chat-word.svg'
    import chatExcel from '@/assets/icons/svg/chat-excel.svg'
    import chatPpt from '@/assets/icons/svg/chat-ppt.svg'
    import chatTxt from '@/assets/icons/svg/chat-txt.svg'
    import chatPdf from '@/assets/icons/svg/chat-pdf.svg'

    const { proxy } = getCurrentInstance();
    const baseUrl = import.meta.env.VITE_APP_BASE_API;
    const emit = defineEmits();

    const props = defineProps({
        messageItemManager:{
            type:Object,
            required: false
        },
        //服务路径
        chatUrl:{
            type:String,
            required:true
        },
        currentInstruct:{
            type:Object,
            required:true
        },
        //是否展示前言
        prefaceState:{
            type:Boolean,
            required:false,
            default:false
        },
        //前言高度
        prefaceHeight:{
            type:Number,
            required:false,
            default:0
        },
        //是否展示title
        isTitle:{
            type:Boolean,
            required:false,
            default:false
        },
        //是否展示用户输入表单
        isKeyList:{
            type:Boolean,
            required:false,
            default:false
        },
        //是否是调试
        isDebugging:{
            type:Boolean,
            required:false,
            default:false
        },
        //是否是嵌入网页
        isPublicVisit:{
            type:Boolean,
            required:false,
            default:false
        }
    });

    let source = null

    //消息列表ref
    const recordsBox = ref(null)
    const isMessageEnding = ref(false)

    const userBoxClassName = "user-record-item";
    const userContentClassName = "user-content";

    const robotBoxClassName = "robot-record-item";
    const robotContentClassName = "robot-content";
    //是否展示开场预置问题
    const isOpenQuestionShow = ref(true)

    //用户输入框的ref元素
    const userInputTextarea = ref(null)
    //用户输入框的提示
    const toolTipContent = ref('')
    //是否展示用户输入时的提示
    const userInputIsShowToolTip = ref(false)
    //用户输入框的提示的ref元素
    const toolTip = ref(null)
    //用户输入框的提示的偏移量
    const textAreaIndent = ref('0px')
    //用户输入框的placeholder
    const userInputTextareaPlaceholer=ref('选择Agent后，输入')
    if(props.isPublicVisit){
        userInputTextareaPlaceholer.value = ''
    }
    //当输入框内容过多时，tip的偏移量
    const toolTipTranslateY = ref('translateY(0px)')
    //发送消息的禁用状态
    const isDisabledSend = ref(true)
    //用户输入框的值
    const userInputTextareaValue  = ref('')
    //文件
    const fileVisible = ref(false)
    const fileStorage = ref(null)
    //是否需要刷新历史记录
    const needRefreshConversation = ref(false)

    const userStore = useUserStore()
    const conversationId = ref(null)
    const conversationInfo = ref({})
    //是否展示用户输入列表
    const isKeyList = ref(false)
    const keyList = ref({})
    const is_done = ref(true)
    //是否是历史记录
    const isHistory = ref(false)

    //是否展示前言
    const prefaceState = ref(false)
    watch(() => props.prefaceState, val => {
        if (val) {
            prefaceState.value = true
            return true
        } else {
            prefaceState.value = false
            return false
        }
    },{ deep: true, immediate: true });

    //前言高度
    const prefaceHeight = ref(0)
    watch(() => props.prefaceHeight, val => {
        if (val) {
            prefaceHeight.value = val
            return val
        } else {
            prefaceHeight.value = 0
            return 0
        }
    },{ deep: true, immediate: true });

    
    //清空输入框内容
    const clearTextAreaInfo = () => {
        toolTipContent.value = ''
        userInputIsShowToolTip.value = false
        textAreaIndent.value = '0px'
        toolTipTranslateY.value = 'translateY(0px)'
    }

    //选中智能体或团队
    const clickInstructItem = () => {
        if(!currentInstruct.value.interType || currentInstruct.value.interType==1){
            toolTipContent.value ='选中【'+currentInstruct.value.applicationName+'】'
            userInputIsShowToolTip.value = true
            nextTick(() => {
                textAreaIndent.value = toolTip.value.clientWidth + 5 +'px'
                userInputTextareaPlaceholer.value = ''
                userInputTextarea.value.focus()
            })
        }else{
            // 是否展示前言 （是）
            prefaceState.value = false
            emit("updatePrefaceState", prefaceState.value);
            userInputTextareaPlaceholer.value = ''
            clearTextAreaInfo()
        }
    }

    //人工介入上传文件，图片
    const isSupportImage = ref(false)
    const supportImageLimit = ref(8)
    const isSupportFile = ref(false)
    //上传图片
    const uploadImageList = ref([])
    //上传文件
    const uploadFileList = ref([])

    watch(() => uploadImageList.value, val => {
        if(val){
            if(isSupportImage.value && val.length>0){
                isDisabledSend.value = false
            }else{
                isDisabledSend.value = true
            }
        }
        else{
            isDisabledSend.value = true
        }
    },{ deep: true, immediate: true });
    watch(() => uploadFileList.value, val => {
        if(val){
            if(isSupportFile.value && val.length>0){
                isDisabledSend.value = false
            }else{
                isDisabledSend.value = true
            }
        }
        else{
            isDisabledSend.value = true
        }
    },{ deep: true, immediate: true });


        
    //当前点击选中的指令
    const currentInstruct = ref({})
    const isMoveScroll = ref(true)
    //历史记录
    const historyHandle = () => {
        isHistory.value = false
        isKeyList.value=false
        isOpenQuestionShow.value=false
        // 是否展示前言
        prefaceState.value = false
        isMoveScroll.value = true
        emit("updatePrefaceState", prefaceState.value);
        clearTextAreaInfo()
        conversationId.value = currentInstruct.value.conversation.id
        conversationHistory(currentInstruct.value.appId,conversationId.value).then(response => {
            props.messageItemManager.loadHistoryMessage(response.data)
        })
    }

    const reload = ref(false)
    watch(() => props.currentInstruct, val => {
        if(val){
            currentInstruct.value = val
            if(currentInstruct.value.user_input_form && currentInstruct.value.user_input_form.length>0 && props.isKeyList){
                isKeyList.value = true
            }
            isHistory.value = val.isHistory?true:false
            if(!isHistory.value){
                if(!props.isPublicVisit){
                    clickInstructItem()
                }
            }else{
                userInputTextareaPlaceholer.value = ''
                historyHandle()
            }
        }
        else{
            currentInstruct.value = {}
        }
    },{ deep: true, immediate: true });

    //删除图片
    const delImage = (img) => {
        uploadImageList.value = uploadImageList.value.filter(x=>x.url!=img.url)
    }
    //删除文件
    const delFile = (file) => { 
        uploadFileList.value = uploadFileList.value.filter(x=>x.url!=file.url)
    }

    //连接状态
    const connectState = ref(false)
    //人工介入
    const ishumanInput = ref(false)
    //输入框输入时的事件
    const handleInput = () => {
        if(userInputTextareaValue.value && !props.messageItemManager.waitting.value && is_done.value && (!connectState.value || ishumanInput.value)){
            isDisabledSend.value = false
        }else{
            isDisabledSend.value = true
        }
    }

    //输入框滑动事件
    const handleScroll = () => {
        if (userInputTextarea.value) {
            toolTipTranslateY.value = 'translateY(-' + userInputTextarea.value.scrollTop + 'px)'
        }  
    }

    //人工介入提交 -文本
    const saveUserMessageMethod = () => {
        let query={
            conversation_id:conversationId.value,
            node_id:node_id.value,
            app_id:currentInstruct.value.appId,
            variable_key:'text',
            variable_value:userInputTextareaValue.value
        }
        saveUserMessage(query).then(response=>{
            props.messageItemManager.pushUserMessage(userInputTextareaValue.value,{});
            userInputTextareaValue.value = ''
            props.messageItemManager.do();
            props.messageItemManager.waittingStart();
            isDisabledSend.value = true;
            tempTxt.value=''
        })
    }
    //人工介入提交 -文本，图片，文件
    const saveUserMessageFileMethod = () => {
        let query={
            conversation_id:conversationId.value,
            node_id:node_id.value,
            app_id:currentInstruct.value.appId,
            variable_list:[
                {
                    variable_key:'text',
                    variable_value:userInputTextareaValue.value
                }
            ]
        }
        let annex = {}
        if(isSupportImage.value && uploadImageList.value && uploadImageList.value.length>0){
            annex.uploadImageList = uploadImageList.value.map((item)=>{return item.url })
            query.variable_list.push({
                variable_key:'files',
                variable_value:uploadImageList.value.map((item)=>{return item.url }).join('\n')
            })
        }
        if(isSupportFile.value && uploadFileList.value && uploadFileList.value.length>0){
            annex.uploadFileList = uploadFileList.value
            query.variable_list.push({
                variable_key:'files',
                variable_value:uploadFileList.value.map((item)=>{return item.url }).join('\n')
            })
        }
        saveUserMessageFile(query).then(response=>{
            props.messageItemManager.pushUserMessage(userInputTextareaValue.value,annex);
            isSupportImage.value = false
            isSupportFile.value = false
            userInputTextareaValue.value = ''
            uploadImageList.value = []
            uploadFileList.value = []
            props.messageItemManager.do();
            props.messageItemManager.waittingStart();
            isDisabledSend.value = true;
            tempTxt.value=''
        })
    }

    //输入框按下键时触发事件
    const handleKeyDown = (e) => {
        if (e.key === 'Enter') {  
            e.preventDefault();
            if (e.shiftKey) {
                userInputTextareaValue.value += '\n';  
            } else {
                runTest()
            }  
        }
    }

    //用户输入表单验证
    const keyListCheck = () => {
        if(currentInstruct.value.user_input_form && currentInstruct.value.user_input_form.length>0 && props.isKeyList){
            for(let i=0;i<currentInstruct.value.user_input_form.length;i++){
                let singleStrip = (currentInstruct.value.user_input_form[i]['text-input']?currentInstruct.value.user_input_form[i]['text-input'] :null)
                    ||(currentInstruct.value.user_input_form[i]['paragraph']?currentInstruct.value.user_input_form[i]['paragraph'] :null)
                    ||(currentInstruct.value.user_input_form[i]['number']?currentInstruct.value.user_input_form[i]['number'] :null)
                    ||(currentInstruct.value.user_input_form[i]['select']?currentInstruct.value.user_input_form[i]['select'] :null)
                if(singleStrip && !keyList.value[singleStrip.variable] && singleStrip.required){
                    ElMessage.warning(singleStrip.label+'必须填写')
                    return false
                }
                if((Object.keys(currentInstruct.value.user_input_form[i])[0] =='text-input' 
                || Object.keys(currentInstruct.value.user_input_form[i])[0] =='paragraph')
                && keyList.value[singleStrip.variable] 
                && keyList.value[singleStrip.variable].length>singleStrip.max_length){
                    ElMessage.warning(singleStrip.label+'长度不能大于'+singleStrip.max_length)
                    return false
                }
            }
        }
        return true
    }

    const task_id = ref(null)
    const node_id = ref(null)

    const printBotMessage = (message,role,is_done) => {
        return new Promise((resolve) => {
            props.messageItemManager.do();
            props.messageItemManager.waittingEnd();
            tempTxt.value=message.answer
            props.messageItemManager.appendNowMessageContent(tempTxt.value,role,false);
            if(is_done){
                props.messageItemManager.messageReceiveDone();
            }
        })
    }

    //记录上一条回复状态
    // const event = ref('')
    const node_type = ref('')
    const role = ref({})
    const ctrl = new AbortController();
    const tempTxt = ref('')
    const initConnect = () => {
        props.messageItemManager.pushUserMessage(userInputTextareaValue.value,{});
        return new Promise((resolve,reject) => {
            isDisabledSend.value = true;
            let query = {
                response_mode:'streaming',
                inputs:keyList.value,
                query: userInputTextareaValue.value,
            }
            if(conversationId.value){
                query.conversation_id = conversationId.value
            }

            let queryJson = JSON.stringify(query)
            userInputTextareaValue.value = ''
            // 创建EventSource对象来监听SSE事件
            source = fetchEventSource(currentInstruct.value.chatUrl,{
                method: 'POST',
                headers:{
                    'Content-Type': 'application/json',
                    'Authorization':'Bearer ' + getToken(),
                },
                body:queryJson,
                openWhenHidden: true,
                signal: ctrl.signal,
                async onopen(response) {
                    console.log('sse连接已打开'); 
                    connectState.value = true
                    // isDisabledSend.value = false;
                    resolve();
                },
                onmessage(message) {
                    if(message.data){
                        const obj = JSON.parse(message.data)
                        task_id.value = obj.task_id
                        if(obj.event == 'workflow_started'){
                            //重命名
                            if(!conversationId.value){
                                conversationRenameMethod(obj.conversation_id)
                            }
                            conversationId.value = obj.conversation_id
                        }
                        //大模型
                        if(obj.event=='node_started' && (obj.data.node_type=='llm' || obj.data.node_type=='human-input-choice' 
                        || obj.data.node_type=='human-input-text' || obj.data.node_type=='human-input-image-files' || obj.data.node_type=='human-input-files')){
                            role.value.name = obj.data.title
                        }
                        //message节点
                        if(obj.event=='message'){
                            props.messageItemManager.waittingEnd();
                            is_done.value = false
                            isDisabledSend.value = true;
                            printBotMessage(obj,JSON.parse(JSON.stringify(role.value)),false)
                        }
                        //工作流节点
                        if((obj.event=='node_started' || obj.event=='node_finished')){
                            props.messageItemManager.workflowHandle(obj.data,obj.event)
                        }
                        //人工介入
                        if(obj.event=='node_finished' && (obj.data.node_type=='human-input-text' || obj.data.node_type=='human-input-choice' 
                        || obj.data.node_type=='human-input-image-files' || obj.data.node_type=='human-input-files')){
                            node_id.value = obj.data.node_id
                            isDisabledSend.value = true;
                            props.messageItemManager.humanIntervention(obj,JSON.parse(JSON.stringify(role.value)))
                            //关闭wait状态
                            props.messageItemManager.waittingEnd();
                            props.messageItemManager.stop();
                            tempTxt.value=''
                            //文本
                            if(obj.data.node_type=='human-input-text'){
                                //暂时可输入
                                ishumanInput.value = true
                            }
                            //图片
                            if(obj.data.node_type=='human-input-image-files'){
                                //暂时可输入
                                ishumanInput.value = true
                                isSupportImage.value = true
                                supportImageLimit.value = obj.data.inputs.limit?obj.data.inputs.limit:8
                            }
                            //文件
                            if(obj.data.node_type=='human-input-files'){
                                //暂时可输入
                                ishumanInput.value = true
                                isSupportFile.value = true
                            }
                            is_done.value = true
                        }
                        //调用停止响应后消息结束
                        if(obj.event=='node_finished' && obj.data.node_type=='llm' && obj.data.status=='failed'
                        && props.messageItemManager.nowMessageHtml.value!=''
                        ){
                            node_type.value == 'answer' 
                            obj.answer = ''
                            printBotMessage(obj,JSON.parse(JSON.stringify(role.value)),true)
                            tempTxt.value=''
                            is_done.value = true
                            role.value.name = ''
                        }
                        //判断该条消息结束
                        if(obj.event=='node_finished' && obj.data && obj.data.node_type && obj.data.node_type=='answer'
                        && props.messageItemManager.nowMessageHtml.value!=''
                        ){
                            node_type.value == 'answer' 
                            obj.answer = ''
                            printBotMessage(obj,JSON.parse(JSON.stringify(role.value)),true)
                            tempTxt.value=''
                            is_done.value = true
                            role.value.name = ''
                            props.messageItemManager.waittingStart();
                            props.messageItemManager.do();
                        }
                        //调用停止响应后流程结束
                        if(obj.event=='workflow_finished' && obj.data.status=='stopped' && props.messageItemManager.nowMessageHtml.value!=''){
                            node_type.value == 'answer' 
                            obj.answer = ''
                            printBotMessage(obj,JSON.parse(JSON.stringify(role.value)),true)
                            tempTxt.value=''
                            is_done.value = true
                            role.value.name = ''
                        }
                        //整个流程结束
                        if(obj.event == 'message_end'){
                            if(currentInstruct.value){
                                props.messageItemManager.appendRetrieverResources(obj.metadata);
                                if(connectState.value && currentInstruct.value.suggested_questions_after_answer.enabled){
                                    getSuggestedQuestionsMethod(obj.message_id)
                                }
                            }
                        }
                    }
                },
                onclose() {
                    isDisabledSend.value = true;
                    //关闭wait状态
                    props.messageItemManager.waittingEnd();
                    props.messageItemManager.stop();
                    connectState.value = false
                    task_id.value = null
                    console.log('sse连接已断开');
                },
                onerror(err) {
                    isDisabledSend.value = true;
                    console.error('发生错误：', err);
                    ctrl.abort();
                    connectState.value = false
                    props.messageItemManager.waittingEnd();
                    throw err;
                }
            })
        })
    }

    const customSend = (message) => {
        props.messageItemManager.do();
        props.messageItemManager.waittingStart();
        isDisabledSend.value = true;
        tempTxt.value=''
    }

    //发送消息 会话型
    const sendMessage = () => {
        if(props.messageItemManager.loading.value){
            ElMessage.warning('正在处理中，请稍后再试')
            return;
        }
        if(userInputTextareaValue.value){
            isKeyList.value=false
            isOpenQuestionShow.value=false
            // 是否展示前言
            prefaceState.value = false
            emit("updatePrefaceState", prefaceState.value);
            clearTextAreaInfo()
            initConnect().then(() => {
                customSend({},false)
            })
        }
    }

    //点击发送
    const runTest = async () =>{
        if(!isDisabledSend.value && (!connectState.value || ishumanInput.value)){
            if(!connectState.value){
                if(currentInstruct.value && userInputTextareaValue.value){
                    if(!isHistory.value){
                        //用户表单校验
                        if(!keyListCheck()){
                            return
                        } 
                    }
                }
                
                if(currentInstruct.value && userInputTextareaValue.value){
                    sendMessage()
                }
            }
            if(ishumanInput.value){
                ishumanInput.value = false
                saveUserMessageFileMethod()
            }
        }
    }

    //停止响应
    const stopRespondingTeam = () =>{
        stopResponding(currentInstruct.value.appId,task_id.value).then(response=>{
        })
    }

    //选择开场问题
    const openQuestionClick = (openQuestion) => {
        //用户表单校验
        if(!keyListCheck()){
            return
        }
        userInputTextareaValue.value = openQuestion
        isDisabledSend.value = false
        runTest()
    }

    //选项处理
    const choicesClick = (choices) => {
        userInputTextareaValue.value=choices
        saveUserMessageFileMethod()
    }

    //建议问题处理
    const autosuggestionClick = (autosuggestion) => {
        if(!props.messageItemManager.waitting.value && is_done.value)
        {
            userInputTextareaValue.value=autosuggestion
            sendMessage()
            suggested_questions.value = []
        }
    }

    const { toClipboard } = useClipboard();
    //复制
    const copyClick = async item => {
      try {
        await toClipboard(item);
        proxy.$modal.msgSuccess("复制成功");
      } catch (e) {
        console.error(e);
      }
    };

    //推荐问题
    const suggested_questions = ref([])
    //获取推荐问题
    const getSuggestedQuestionsMethod = (message_id) => {
        getSuggestedQuestions(currentInstruct.value.appId,message_id).then(response=>{
            suggested_questions.value = response.data
        })
    }

    //重命名会话
    const conversationRenameMethod = (conversation_id) => {
        let query = {auto_generate:true}
        conversationRename(currentInstruct.value.appId,conversation_id,query).then(response=> {
            emit('conversationRefresh')
        })
    }

    //关闭会话型连接
    async function closeSourceSessionMethod(){
       await closeSourceSession(currentInstruct.value.appId,task_id.value).then(response => {
            // console.log('sse连接已断开');
        })
    }

    //清除处理
    async function SessionClear(){
        if(task_id.value){
           await closeSourceSessionMethod()
        }
        //是否展示开场预置问题
        isOpenQuestionShow.value=true
        //是否可以发送信息 （否）
        isDisabledSend.value = true
        //输入框内容清空
        userInputTextareaValue.value = ''
        ishumanInput.value = false
        isSupportFile.value = false
        isSupportImage.value = false
        uploadFileList.value = []
        uploadImageList.value = []
        isMoveScroll.value = true
        clearTextAreaInfo()
        
    }

    //当前应用新会话
    async function openNewSession(){
        await SessionClear()
        Object.keys(keyList.value).forEach(element => {
            keyList.value[element]=''
        });
        conversationId.value = null
        //是否展示用户输入
        isKeyList.value = true
        //是否是历史会话记录
        isHistory.value = false
        props.messageItemManager.new()
        //历史记录取消选中
        emit('conversationUnselect')
    }
    //新会话清除选中应用
    async function openNewSessionClear(){
        await SessionClear()
        keyList.value = []
        conversationId.value = null
        //选中的团队或智能体清空
        currentInstruct.value = null  
        // 是否展示前言 （是）
        prefaceState.value = true
        emit("updatePrefaceState", prefaceState.value);
        //是否是历史会话记录
        isHistory.value = false
        props.messageItemManager.new()
        //历史记录取消选中
        emit('conversationUnselect')
    }

    async function openHistorySessionClear(){
        await SessionClear()
        keyList.value = []
        //选中的团队或智能体清空
        currentInstruct.value = null  
        //是否是历史会话记录
        isHistory.value = true
        props.messageItemManager.new()
    }

    const closeSession = () => {
        openNewSession()
    }

    defineExpose({
        closeSession,
        openNewSessionClear,
        openHistorySessionClear
    })

    onBeforeUnmount(()=>{
        closeSession()
    })

    //定位到最新内容
    const moveScroll = () => {
        nextTick(()=> {
            recordsBox.value.scrollTop = recordsBox.value.scrollHeight;
        })
    }

    watch(props.messageItemManager.nowMessageHtml, () => {
        moveScroll();
    })

    watch(props.messageItemManager.nowMessageLine, () => {
        moveScroll();
    });

    watch(props.messageItemManager.refMessageItemList, (newValue, oldValue) => {
        if(newValue.length!=oldValue.length){
            isMoveScroll.value = true
        }
        if (isMoveScroll.value) {
            moveScroll()
        }
    });

    watch(props.messageItemManager.waitting, () => {
        moveScroll();
    });

    watch(suggested_questions, () => {
        moveScroll();
    });

    onMounted(() => {
        if (userInputTextarea.value) {  
            userInputTextarea.value.addEventListener('scroll', handleScroll);
        }  
    })

    onUnmounted(() => {
        if (userInputTextarea.value) {  
            userInputTextarea.value.removeEventListener('scroll', handleScroll);  
        }  
    })

</script>
<style lang="scss" scoped>
.container{
    position: relative;
    width: 100%;
    .content{
        text-align: center;
        width: 100%;
        display: flex;
        flex-direction: column;
        .top{
            height: 80px;
            line-height: 80px;
            font-size: 30px;
            color: #5050E6;
            font-weight: 550;
        }
        .ai-chat-content{
            overflow-y: auto;
            overflow-x: hidden;
            .user-input{
                background-color: white;
                padding:20px 20px 1px 20px;
                border-radius: 10px;
                .key-input{
                    font-size: 15px;
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 20px;
                    // align-items: center;
                    text-align: left;
                }
            }
            .chat-message{
                display: flex;
                flex-direction: column;
                .user-chat-area{
                    align-self: flex-end;
                    display: flex;
                    flex-direction: column;
                    margin-bottom: 10px;
                    padding-left: 0;
                    .user-box{
                        display: flex;
                        flex-direction: row;
                        justify-content: end;
                        align-items: center;
                        margin-bottom: 3px;
                        .user-name{
                            line-height: 34px;
                            height: 34px;
                            color: #B2B2B2;
                            font-size: 13px;
                        }
                        .user-icon{
                            img{
                                width:30px;
                                height:30px;
                                background-color: #6977F1;
                                border-radius: 5px;
                                border: 1px solid #E6E6E6;
                            }
                            margin-left:4px;
                        }
                    }
                    .user-chat-message{
                        background-color: #5050E6;
                        border-radius: 12px;
                        border-top-right-radius: 0;
                        box-sizing: border-box;
                        color: #fff;
                        // cursor: pointer;
                        display: inline-block;
                        font-size: 14px;
                        line-height: 22px;
                        min-height: 26px;
                        outline: none;
                        padding: 9px 14px;
                        white-space: normal;
                        word-break: break-word;
                    }
                }
                .robot-chat-area{
                    align-items: flex-start;
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;
                    margin-bottom: 10px;
                    position: relative;
                    .robot-box{
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        margin-bottom: 3px;
                        .robot-name{
                            line-height: 34px;
                            height: 34px;
                            color: #B2B2B2;
                            font-size: 13px;
                        }
                        .robot-icon{
                            img{
                                width:30px;
                                height:30px;
                                background-color: #6977F1;
                                border-radius: 5px;
                                border: 1px solid #E6E6E6;
                            }
                                margin-right:4px;
                            // background-color: #dfdfff
                        }
                    }
                    .robot-chat-message{
                        background-color: #F9FBFC;
                        border-radius: 0 12px 12px;
                        max-width: 100%;
                        padding: 12px 14px;
                        // width: 100%;
                        box-sizing: border-box;
                        overflow: hidden;
                        ::v-deep(.github-markdown-body) {
                            padding: 0;
                            font-size: 13px !important;
                            p{
                                margin-bottom: 0;
                            }
                        }
                    }
                }
            }
            .open-question-box{
                margin-top: 20px;
                display: flex;
                flex-direction: column;
                .prologue{
                    border:1px solid #E9EAEE;
                    border-radius: 10px;
                    background-color: white;
                    color: black;
                    padding: 10px 20px;
                    font-size: 15px;
                    text-align:left;
                    margin-bottom: 5px;
                }
                .open-question{
                    border:1px solid #E9EAEE;
                    border-radius: 10px;
                    background-color: white;
                    color: #7A8698;
                    padding: 10px 20px;
                    display: inline-block;
                    font-size: 15px;
                    max-width: 100%;
                    text-align: left;
                    cursor: pointer;
                }
            }
            .record-box-user {
                width: calc(100%);
                display: inline-flex;
                .head-img{
                    margin-left: 10px;
                    img{
                        height: 40px;
                        width: 40px;
                        // border: 2px solid #E6E6E6;
                        border-radius: 8px;
                        background-color: #dfdfff;
                        margin:0;
                    }
                }
            
            }
            .record-box-robot {
                width: calc(100%);
                // margin-left: 10px;
                display: inline-flex;
                .head-img{
                    height: 40px;
                    width: 44px;
                    border-radius: 8px;
                    margin-right: 10px;
                    img{
                        height: 40px;
                        width: 40px;
                        // border: 2px solid #E6E6E6;
                        border-radius: 8px;
                        background-color: #dfdfff;
                        margin:0;
                    }
                }
                ::v-deep .el-loading-mask {
                    position: absolute;
                    z-index: 2000;
                    background-color: #f5f0f08f;
                    margin: 0;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    transition: opacity var(--el-transition-duration);
                    height: 40px;
                    width: 41px;
                    border-radius: 10px;
                    padding: 7px;
                }
                ::v-deep .el-loading-spinner {
                    top: 50%;
                    margin-top: calc((0px - var(--el-loading-spinner-size)) / 2);
                    width: 100%;
                    text-align: center;
                    position: absolute;
                    width: 41px;
                    height: 40px;
                    /* align-items: center; */
                    display: flex;
                    align-items: center;
                }
                ::v-deep .el-loading-spinner .circular {
                    display: inline;
                    -webkit-animation: loading-rotate 2s linear infinite;
                    animation: loading-rotate 2s linear infinite;
                    height: 23px;
                    width: 23px;
                }
            
            }
            .record-item {
                display: inline-block;
                border-radius: 10px;
                background-color: #fff;
                padding: 8px 12px;
                max-width: calc(100% - 60px);
                margin-bottom: 10px;
                font-size: 15px;
            }
            .user-record-box {
                @extend .record-box-user;
                justify-content: flex-end;
                text-align: right;
                float: right;
            }
            .robot-record-box {
                @extend .record-box-robot;
                text-align: left;
                .workflow-info-box{
                    margin-bottom: 5px;
                    display: inline-block;
                    .workflow-info{
                        background-color: #ECFDF3;
                        border-radius: 5px;    
                        margin: 5px 0 5px 0;
                        cursor: pointer;
                        .title{
                            color: #667085;
                            font-size: 12px;
                            display: flex;
                            align-items: center;
                            padding: 7px 10px;
                            .img{
                                width: 15px;
                                height: 15px;
                            }
                        }
                        .node-box{
                            background-color: white;
                            border: 1px solid #F7F8FA;
                            padding: 5px 10px;
                            margin-bottom: 5px;
                            border-radius: 5px;
                            width:100%;
                            color: #5A6677;
                            font-weight: 600;
                            .node{
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                font-size: 12px;
                            }
                        }
                    }
                }
            }
            .user-name{
                line-height: 20px;
                height: 20px;
                color: #B2B2B2;
                font-size: 12px;
            }
            .robot-record-item {
                @extend .record-item;
                width: calc( 100% - 60px );
                color: #032220;
                background-color: #F9FBFC;
                font-size: 14px;
                font-weight: 500;
                position:relative;
                &:hover{
                    .hidden-area{
                        display:block;
                    }
                }
                .hidden-area{
                    cursor:auto;
                    display:none;
                    top:-10px;
                    right:-45px;
                    position:absolute;
                    width: 100%;
                    // padding:10px 20px;
                    .operation{
                        display: flex;
                        justify-content: right;
                        .box{
                            background-color: white;
                            padding: 4px 3px;
                            border-radius: 5px;
                            border: 1px solid #E9EBEF;
                        }
                    }
                }
            }
            .stop-responding{
                border: 1px solid #E9EAEE;
                border-radius: 10px;
                background-color: white;
                color: #5050E6;;
                padding: 10px 20px;
                display: flex;
                font-size: 15px;
                align-items: center;
                justify-content: center;
                cursor: pointer;
            }
            .user-record-item {
                @extend .record-item;
                color: white;
                background-color: #5050e6;
                text-align: left;
                font-size: 14px;
                font-weight: 500;
            }
            .user-content{
                display: flex;
                flex-direction: column;
                align-items: end;
                width: 100%;
            }
            .robot-content {
                display: flex;
                flex-direction: column;
                align-items: start;
                width: 100%;
            }
            .process-status{
                .status{
                    background-color: #F9FBFC;
                    padding: 10px;
                    border-radius: 10px;
                    margin-bottom: 5px;
                    color: #19CD56;
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                }
                .process{
                    color: #032220;
                    background-color: #F9FBFC;
                    margin-bottom: 5px;
                    border-radius: 10px;
                    width: calc( 100% - 60px );
                    .process-top{
                        display: flex;
                        justify-content: space-between;
                        align-items:center;
                        font-size:18px;
                        padding: 8px 12px;
                        background-color: white;
                        border-top-left-radius: 10px;
                        border-top-right-radius: 10px;
                        cursor: pointer;
                        .process-left{
                            display: flex;
                            align-items: center;
                        }
                    }
                    .process-content{
                        padding: 5px;
                        .process-title{
                            display: flex;
                            align-items: center;
                            // margin-left: 5px;
                            padding: 10px;
                            background-color: white;
                            border-radius: 10px;
                            cursor: pointer;
                            img{
                                height: 20px;
                                width: 20px;
                                margin-right: 10px;
                            }
                            &:hover{
                                background-color: #F0F0F5;
                            }
                        }
                        .process-child-status{
                            background-color: #F7F7FA;
                            padding: 5px 15px;
                            font-size: 13px;
                            line-height: 20px;
                            border-bottom-right-radius: 10px;
                            border-bottom-left-radius: 10px;
                        }
                    }
                    .process-bottom{
                        background-color: #F9FBFC;
                        padding: 10px;
                        border-bottom-left-radius: 10px;
                        border-bottom-right-radius: 10px;
                        .bottom-content{
                            width: 100px;
                            padding: 5px;
                            font-size: 12px;
                            background-color: #ECF7EC;
                            color: #32A252;
                            display: flex;
                            justify-content: center;
                            border-radius: 5px;
                        }
                    }
                }
            }
            .operation{
                display: flex;
                // justify-content: end;
                width: calc( 100% - 60px );
                margin-bottom: 10px;
                margin-top: -5px;
            }
            .knowledge-list{
                color: #032220;
                background-color: #F9FBFC;
                width: calc( 100% - 60px );
                padding: 8px 12px;
                border-radius: 10px;
            }
            .choices{
                background-color: #F5F6F7;
                // min-width: 150px;
                padding: 10px;
                display: flex;
                align-items: center;
                justify-content: left;
                border-radius: 15px;
                border: 1px solid #E8E9ED;
                margin-bottom: 5px;
                color: #032220;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;

                &:hover{
                    background-color: #ececec;
                }
            }
        } 
        .bottom-area{
            background-color: transparent;
            padding-top: 10px;
            bottom: 0;
            // position: absolute;
            text-align: center;
            width: 100%;
            z-index: 11;
            margin-top: auto;
            .bar{
                border-top: 1px solid transparent;
                background-color: transparent;
                box-sizing: border-box;
                padding: 0 16px;
                width: 100%;
                align-items: center;
                display: flex;
                justify-content: flex-start;
                .tools-list{
                    background: transparent;
                    display: flex;
                    height: 48px;
                    padding-top: 10px;
                    .tools-item{
                        background: #fff;
                        box-shadow: 1px 1px 5px 0 #d9d9ff;
                        align-items: center;
                        border-radius: 8px;
                        cursor: pointer;
                        display: flex;
                        height: 30px;
                        margin-right: 8px;
                        outline: none;
                        padding: 6px 8px;
                        .tool-icon{
                            background-repeat: no-repeat;
                            background-size: cover;
                            display: inline-block;
                            height: 20px;
                            width: 20px;
                        }
                        .tool-name{
                            color: #1c1f1e;
                            font-family: PingFangSC-Regular;
                            font-size: 12px;
                            line-height: 12px;
                            margin-left: 6px;
                        }
                    }
                }
            }
            .submit-area{
                // padding: 0 16px 12px;
                box-sizing: border-box;
                width: 100%;
                .content-area{
                    background-image: linear-gradient(90deg, #5050E6, #b6b6ff);
                    border-radius: 12px;
                    box-shadow: 0 0 12px 0 rgba(0,155,109,.15);
                    box-sizing: border-box;
                    height: 100%;
                    padding: 2px;
                    width: 100%;
                    .user-input-area{
                        border: 0 !important;
                        border-radius: 10px;
                        padding: 8px 10px;
                        position: relative;
                        transition: opacity .5s;
                        width: 100%;
                        background-color: #fff;
                        .upload-file-handle{
                            text-align: left;
                            padding: 5px 0;
                            border-bottom: 1px solid #F1F0F5;
                            margin-bottom: 5px;
                            display: flex;
                            align-items: center;
                        }
                        .top-area{
                            display: flex;
                            position: relative;
                            .text-area{
                                line-height: 1.4;
                                flex: 1;
                                font-size: 14px;
                                overflow: hidden;
                                position: relative;
                                .tool-tip{
                                    align-content: center;
                                    align-items: center;
                                    background-color: #f0f2f2;
                                    border-radius: 4px;
                                    display: flex;
                                    height: 26px;
                                    line-height: 26px;
                                    padding-left: 10px;
                                    padding-right: 0;
                                    cursor: text;
                                    left: 0;
                                    position: absolute;
                                    top: 0;
                                    font-size: 14px;
                                }
                                .user-input{
                                    font-size: 14px;
                                    height: 62px;
                                    border: none;
                                    box-sizing: border-box;
                                    color: #222;
                                    line-height: 20px;
                                    outline: 0;
                                    overflow-y: auto;
                                    width: 100%;
                                }
                                .show-tool-tip{
                                    line-height: 26px;
                                }
                            }
                            textarea{
                                font: 100% arial, helvetica, clean;
                                overflow: auto;
                                vertical-align: top;
                                resize: none;
                            }
                        }
                        .bottom-info-area{
                            display: flex;
                            justify-content: flex-end;
                            margin-top: 5px;
                            .send-btn{
                                background: linear-gradient(316deg, #5050E6 16.71%, #5050E6 116.53%);
                                border-radius: 10px;
                                height: 26px;
                                position: relative;
                                width: 36px;
                                &:after{
                                    background: url(https://edu-wenku.bdimg.com/v1/pc/aigc/presentation-sample/send-1702458556573.svg) no-repeat;
                                    background-size: cover;
                                    content: "";
                                    height: 16px;
                                    position: absolute;
                                    top: 50%;
                                    transform: translate(-50%, -50%);
                                    width: 16px;
                                }
                            }
                            .disabled{
                                cursor: not-allowed;
                            }
                        }
                    }
                }
            }
            .tip{
                line-height: 25px;
                font-size: 12px;
                text-align: left;
                color: #9E9EBB;
            }
        } 
        .upload-file-show-send{
            max-height: 100px;
            overflow-y:auto;
            overflow-x: hidden;
        }
        .upload-file-show{
            display: flex;
            flex-direction: column;
            padding-bottom: 10px;
            .file-justify-content-send{
                justify-content: flex-start;
            }
            .file-justify-content{
                justify-content: right;
            }
            .file{
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                .file-show-background-send{
                    background-color: #f9f8fd;
                }
                .file-show-background{
                    background-color: white;
                }
                .file-show{
                    height: 60px;
                    width: 200px;
                    margin-top: 10px;
                    margin-right: 5px;
                    padding: 10px;
                    position:relative;
                    border-radius: 8px;
                    font-size: 14px;
                    .whole{
                        display: flex;
                        justify-content: space-between;
                        .left{
                            img{
                                width: 30px;
                            }
                        }
                        .right{
                            .title{
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                width: 140px;
                                text-align: left;
                            }
                        }
                    }
                    .del{
                        top:-5px;
                        right:-4px;
                        position:absolute;
                        cursor: pointer;
                        color: red;
                    }
                }
            }
            .image-justify-content-send{
                justify-content: flex-start;
            }
            .image-justify-content{
                justify-content: right;
            }
            .image{
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                .image-show{
                    height: 100px;
                    width: 100px;
                    margin-top: 10px;
                    margin-right: 5px;
                    position:relative;
                    .img{
                        height: 100px;
                        width: 100px;
                        border-radius: 8px;
                    }
                    .del{
                        top:-5px;
                        right:-4px;
                        position:absolute;
                        cursor: pointer;
                        color: red;
                    }
                }
            }
        }
    }
    .coverage-area{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: calc(100vh - 190px);
        background-color: #fffffff5;
        padding: 20px;
        .title{
            display: flex;
            align-items: center;
            font-size: 19px;
            font-weight: 600;
        }
        .describe{
            margin-top: 20px;
            font-size: 15px;
            font-weight: 500;
        }
        .buttons{
            margin-top: 20px;
            display: flex;
            justify-content: end;
        }
    }
}

</style>
